{"log": {"version": "1.1", "creator": {"name": "Firebug", "version": "1.5X.0b8"}, "browser": {"name": "Firefox", "version": "3.6b6pre"}, "pages": [{"startedDateTime": "2010-01-02T15:39:42.040+01:00", "id": "page_7689", "title": "Cuzillion", "pageTimings": {"onContentLoad": 531, "onLoad": 5176}, "comment": "Default limit on number of concurrent requests was set to 6 in this case. See the 7th and 8th request (click this bar to see the list of requests), these were blocked till the first and second finished."}, {"startedDateTime": "2010-01-02T16:40:21.935+01:00", "id": "page_12043", "title": "Cuzillion", "pageTimings": {"onContentLoad": 530, "onLoad": 9591}, "comment": "The limit was set to 2 for this test case."}], "entries": [{"pageref": "page_7689", "startedDateTime": "2010-01-02T15:39:42.040+01:00", "time": 440, "request": {"method": "GET", "url": "http://stevesouders.com/cuzillion/?c0=bi1hfff2_0_f&c1=bi1hfff2_0_f&c2=bi1hfff2_0_f&c3=bi1hfff2_0_f&c4=bi1hfff2_0_f&c5=bi1hfff2_0_f&c6=bi1hfff2_0_f&c7=bi1hfff2_0_f&t=1258547264277", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Host", "value": "stevesouders.com"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows; U; Windows NT 6.0; en-US; rv:1.9.2b6pre) Gecko/20091230 Namoroka/3.6b6pre (.NET CLR 3.5.30729)"}, {"name": "Accept", "value": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"}, {"name": "Accept-Language", "value": "en-us,en;q=0.5"}, {"name": "Accept-Encoding", "value": "gzip,deflate"}, {"name": "Accept-<PERSON><PERSON><PERSON>", "value": "ISO-8859-1,utf-8;q=0.7,*;q=0.7"}, {"name": "Keep-Alive", "value": "115"}, {"name": "Connection", "value": "keep-alive"}], "queryString": [{"name": "c0", "value": "bi1hfff2_0_f"}, {"name": "c1", "value": "bi1hfff2_0_f"}, {"name": "c2", "value": "bi1hfff2_0_f"}, {"name": "c3", "value": "bi1hfff2_0_f"}, {"name": "c4", "value": "bi1hfff2_0_f"}, {"name": "c5", "value": "bi1hfff2_0_f"}, {"name": "c6", "value": "bi1hfff2_0_f"}, {"name": "c7", "value": "bi1hfff2_0_f"}, {"name": "t", "value": "1258547264277"}], "headersSize": 552, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Date", "value": "Sat, 02 Jan 2010 14:39:47 GMT"}, {"name": "Server", "value": "Apache"}, {"name": "X-Powered-By", "value": "PHP/5.2.3"}, {"name": "Vary", "value": "Accept-Encoding"}, {"name": "Content-Encoding", "value": "gzip"}, {"name": "Content-Length", "value": "2439"}, {"name": "Keep-Alive", "value": "timeout=2, max=100"}, {"name": "Connection", "value": "Keep-Alive"}, {"name": "Content-Type", "value": "text/html"}], "content": {"size": 11576, "mimeType": "text/html", "text": "\n<script>\nvar gTop = Number(new Date());\nvar gScriptMsg = \"\";\nfunction cuz_addHandler(elem, sType, fn, capture) {\n    capture = (capture) ? true : false;\n    if (elem.addEventListener) {\n        elem.addEventListener(sType, fn, capture);\n    }\n    else if (elem.attachEvent) {\n        elem.attachEvent(\"on\" + sType, fn);\n    }\n    else {\n        // Netscape 4\n        if ( elem[\"on\" + sType] ) {\n            // Do nothing - we don't want to overwrite an existing handler.\n        }\n        else {\n            elem[\"on\" + sType] = fn;\n        }\n    }\n}\nfunction doOnload() {\n\tvar end = Number(new Date());\n    document.getElementById('loadtime').innerHTML = 'page load time: ' + (end - gTop) + ' ms';\n\tif ( gScriptMsg && document.getElementById('loadedscripts') ) {\n\t\tdocument.getElementById('loadedscripts').innerHTML += gScriptMsg;\n\t}\n}\ncuz_addHandler(window, 'load', doOnload);\nvar gbEnabled = false;\nfunction enableEdit() {\n\tif ( gbEnabled ) return;\n\tgbEnabled = true;\n\taddStylesheet('cuzillion.1.1.css');\n\taddScript('cuzillion.1.5.js');\n}\nfunction addStylesheet(url) {\n\tvar stylesheet = document.createElement('link');\n\tstylesheet.rel = 'stylesheet';\n\tstylesheet.type = 'text/css';\n\tstylesheet.href =  url;\n\tdocument.getElementsByTagName('head')[0].appendChild(stylesheet);\n}\nfunction addScript(url) {\n\tvar script = document.createElement('script');\n\tscript.src = url;\n\tdocument.getElementsByTagName('head')[0].appendChild(script);\n}\nfunction scriptSleepOnload(sUrl) {\n\tvar now = Number(new Date());\n\tvar msg = \"<nobr>\" + (now - gTop) + \"ms: \\\"\" + sUrl + \"\\\" done</nobr>\\n\";\n\tif ( document.getElementById('loadedscripts') ) {\n\t\tdocument.getElementById('loadedscripts').innerHTML += msg;\n\t}\n\telse {\n\t\tgScriptMsg += msg;\n\t}\n}\nfunction reloadPage(url) {\n\tdocument.body.innerHTML = '';\n\tdocument.location = url;\n}\nfunction cleanText(sText) {\n    return sText.replace(/<.*?>/g, '');\n}\n</script>\n<html>\n<head>\n<title>Cuzillion</title>\n<link rel=\"icon\" href=\"favicon.ico\" type=\"image/x-icon\">\n<!--\nCopyright 2008 Google Inc.\n\nLicensed under the Apache License, Version 2.0 (the \"License\");\nyou may not use this file except in compliance with the License.\nYou may obtain a copy of the License at\n\n     http://www.apache.org/licenses/LICENSE-2.0\n\nUnless required by applicable law or agreed to in writing, software\ndistributed under the License is distributed on an \"AS IS\" BASIS,\nWITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\nSee the License for the specific language governing permissions and\nlimitations under the License.\n-->\n</head>\n\n<body style='margin: 0px; padding: 0px; font-family: \"Trebuchet MS\", \"Bitstream Vera Serif\", Utopia, \"Times New Roman\", times, serif;'>\n\n<div style=\"background: #333; color: white; padding: 8px;\">\n  <div style=\"float:right; margin-top: 2px;\">\n    <a href=\"help.php#examples\" style=\"color: white; font-size: 0.9em; text-decoration: none;\">Examples</a>&nbsp;|&nbsp;<a href=\"help.php\" style=\"color: white; font-size: 0.9em; text-decoration: none;\">Help</a><br><a href=\"http://stevesouders.com/\" style=\"color: white; font-size: 0.9em; text-decoration: none;\">stevesouders.com</a>\n  </div>\n  <font style=\"font-size: 2em; font-weight: bold; margin-right: 10px;\"><a href=\".\" style=\"color:white; text-decoration: none;\"><img border=0 src=\"logo-32x32.gif\">&nbsp;Cuzillion</a></font><i>'cuz there are a zillion pages to check</i>\n</div>\n\n<div id=content style=\"margin: 8px;\">\n\n\n<!-- begin resources for inbody -->\n<!-- image<p style='margin: 0 4px 4px 12px; font-size: 0.8em;'> on domain1 with a 2 second delay using HTML tags -->\n<img src='http://1.cuzillion.com/bin/resource.cgi?type=gif&sleep=2&n=1&t=1262443187'>\n\n<!-- image<p style='margin: 0 4px 4px 12px; font-size: 0.8em;'> on domain1 with a 2 second delay using HTML tags -->\n<img src='http://1.cuzillion.com/bin/resource.cgi?type=gif&sleep=2&n=2&t=1262443187'>\n\n<!-- image<p style='margin: 0 4px 4px 12px; font-size: 0.8em;'> on domain1 with a 2 second delay using HTML tags -->\n<img src='http://1.cuzillion.com/bin/resource.cgi?type=gif&sleep=2&n=3&t=1262443187'>\n\n<!-- image<p style='margin: 0 4px 4px 12px; font-size: 0.8em;'> on domain1 with a 2 second delay using HTML tags -->\n<img src='http://1.cuzillion.com/bin/resource.cgi?type=gif&sleep=2&n=4&t=1262443187'>\n\n<!-- image<p style='margin: 0 4px 4px 12px; font-size: 0.8em;'> on domain1 with a 2 second delay using HTML tags -->\n<img src='http://1.cuzillion.com/bin/resource.cgi?type=gif&sleep=2&n=5&t=1262443187'>\n\n<!-- image<p style='margin: 0 4px 4px 12px; font-size: 0.8em;'> on domain1 with a 2 second delay using HTML tags -->\n<img src='http://1.cuzillion.com/bin/resource.cgi?type=gif&sleep=2&n=6&t=1262443187'>\n\n<!-- image<p style='margin: 0 4px 4px 12px; font-size: 0.8em;'> on domain1 with a 2 second delay using HTML tags -->\n<img src='http://1.cuzillion.com/bin/resource.cgi?type=gif&sleep=2&n=7&t=1262443187'>\n\n<!-- image<p style='margin: 0 4px 4px 12px; font-size: 0.8em;'> on domain1 with a 2 second delay using HTML tags -->\n<img src='http://1.cuzillion.com/bin/resource.cgi?type=gif&sleep=2&n=8&t=1262443187'>\n\n<!-- end resources for inbody -->\n\n<div id=floattray style=\"float: left; width: 170px; margin-right: 30px;\">\n  <div id=step1text style=\"text-align: left; margin: 0 0 4px 4px; height: 50px; padding-top: 12px;\"></div>\n  <div id=comptray>\n  &nbsp;\n  </div>\n</div>\n\n<div id=pageavatar style=\"float: left; width: 310px; margin-right: 30px;\">\n  <div id=step2text style=\"text-align: left; margin: 0 0 4px 4px; height: 50px; padding-top: 12px;\"></div>\n  <div style=\"background: #CCC; border: 1px solid black; \">\n    <code style=\"font-size: 1.2em; font-weight: bold; color: #666666; display: block;\">&lt;HTML&gt;</code>\n    <code style=\"font-size: 1.2em; font-weight: bold; color: #666666; display: block;\">&lt;HEAD&gt;</code>\n    <div class=\"drop\" style=\"border: 1px solid #EEE; background: #EEE; padding: 12px 0 12px 0; width: 300px; margin: 0 0 0 4px;\">\n\t  <ul style=\"margin: 0; padding: 0;\" id=inhead></ul>\n\t  <div id=inheadTarget></div>\n\t</div>\n    <code style=\"font-size: 1.2em; font-weight: bold; color: #666666; display: block;\">&lt;/HEAD&gt;</code>\n    <code style=\"font-size: 1.2em; font-weight: bold; color: #666666; display: block;\">&lt;BODY&gt;</code>\n    <div class=\"drop\" style=\"border: 1px solid #EEE; background: #EEE; padding: 12px 0 12px 0; width: 300px; margin: 0 0 0 4px;\">\n\t  <ul style=\"margin: 0; padding: 0;\" id=inbody><li onclick='enableEdit()' id='acomp1' class='sortitem image' style='cursor: move; list-style: none; border-width: 2px; border-style: solid; border-color: #555; margin: 4px;'><div id=acomp1Div class='component image' style='padding: 2px; font-family: Arial; text-align: center; display: block; text-decoration: none; color: white; background: #000080; text-align: left;'><span>image<p style='margin: 0 4px 4px 12px; font-size: 0.8em;'> on domain1 with a 2 second delay using HTML tags</span></div>\n<li onclick='enableEdit()' id='acomp2' class='sortitem image' style='cursor: move; list-style: none; border-width: 2px; border-style: solid; border-color: #555; margin: 4px;'><div id=acomp2Div class='component image' style='padding: 2px; font-family: Arial; text-align: center; display: block; text-decoration: none; color: white; background: #000080; text-align: left;'><span>image<p style='margin: 0 4px 4px 12px; font-size: 0.8em;'> on domain1 with a 2 second delay using HTML tags</span></div>\n<li onclick='enableEdit()' id='acomp3' class='sortitem image' style='cursor: move; list-style: none; border-width: 2px; border-style: solid; border-color: #555; margin: 4px;'><div id=acomp3Div class='component image' style='padding: 2px; font-family: Arial; text-align: center; display: block; text-decoration: none; color: white; background: #000080; text-align: left;'><span>image<p style='margin: 0 4px 4px 12px; font-size: 0.8em;'> on domain1 with a 2 second delay using HTML tags</span></div>\n<li onclick='enableEdit()' id='acomp4' class='sortitem image' style='cursor: move; list-style: none; border-width: 2px; border-style: solid; border-color: #555; margin: 4px;'><div id=acomp4Div class='component image' style='padding: 2px; font-family: Arial; text-align: center; display: block; text-decoration: none; color: white; background: #000080; text-align: left;'><span>image<p style='margin: 0 4px 4px 12px; font-size: 0.8em;'> on domain1 with a 2 second delay using HTML tags</span></div>\n<li onclick='enableEdit()' id='acomp5' class='sortitem image' style='cursor: move; list-style: none; border-width: 2px; border-style: solid; border-color: #555; margin: 4px;'><div id=acomp5Div class='component image' style='padding: 2px; font-family: Arial; text-align: center; display: block; text-decoration: none; color: white; background: #000080; text-align: left;'><span>image<p style='margin: 0 4px 4px 12px; font-size: 0.8em;'> on domain1 with a 2 second delay using HTML tags</span></div>\n<li onclick='enableEdit()' id='acomp6' class='sortitem image' style='cursor: move; list-style: none; border-width: 2px; border-style: solid; border-color: #555; margin: 4px;'><div id=acomp6Div class='component image' style='padding: 2px; font-family: Arial; text-align: center; display: block; text-decoration: none; color: white; background: #000080; text-align: left;'><span>image<p style='margin: 0 4px 4px 12px; font-size: 0.8em;'> on domain1 with a 2 second delay using HTML tags</span></div>\n<li onclick='enableEdit()' id='acomp7' class='sortitem image' style='cursor: move; list-style: none; border-width: 2px; border-style: solid; border-color: #555; margin: 4px;'><div id=acomp7Div class='component image' style='padding: 2px; font-family: Arial; text-align: center; display: block; text-decoration: none; color: white; background: #000080; text-align: left;'><span>image<p style='margin: 0 4px 4px 12px; font-size: 0.8em;'> on domain1 with a 2 second delay using HTML tags</span></div>\n<li onclick='enableEdit()' id='acomp8' class='sortitem image' style='cursor: move; list-style: none; border-width: 2px; border-style: solid; border-color: #555; margin: 4px;'><div id=acomp8Div class='component image' style='padding: 2px; font-family: Arial; text-align: center; display: block; text-decoration: none; color: white; background: #000080; text-align: left;'><span>image<p style='margin: 0 4px 4px 12px; font-size: 0.8em;'> on domain1 with a 2 second delay using HTML tags</span></div>\n</ul>\n\t  <div id=inbodyTarget></div>\n\t</div>\n    <code style=\"font-size: 1.2em; font-weight: bold; color: #666666; display: block;\">&lt;/BODY&gt;</code>\n    <code style=\"font-size: 1.2em; font-weight: bold; color: #666666; display: block;\">&lt;/HTML&gt;</code>\n  </div>\n  <div id=loadtime style=\"text-align: left; margin-top: 10px;\"></div>\n  <div id=loadedscripts style=\"text-align: left; margin-top: 10px; width: 300px; font-size: 0.9em;\"></div>\n</div> <!-- end pageavatar -->\n\n<div style=\"position: absolute; left: 560px;\">\n  <div id=step3text style=\"text-align: left; margin: 0 0 4px 4px; height: 50px; padding-top: 12px;\"></div>\n  <div id=pagesubmit style=\"text-align: left;\">\n<nobr>\n<input type=button value=\"Edit\" onclick=\"enableEdit()\">&nbsp;&nbsp;\n<input type=button value=\"Reload\" onclick=\"reloadPage('/cuzillion/?c0=bi1hfff2_0_f&c1=bi1hfff2_0_f&c2=bi1hfff2_0_f&c3=bi1hfff2_0_f&c4=bi1hfff2_0_f&c5=bi1hfff2_0_f&c6=bi1hfff2_0_f&c7=bi1hfff2_0_f&t=1262443187')\">&nbsp;&nbsp;\n<input type=button value=\"Clear\" onclick=\"document.location='.'\">&nbsp;&nbsp;\n</nobr>\n </div>\n</div>\n\n<div style=\"clear: both;\">\n</div>\n\n</div> <!-- content -->\n\n\n\n</body>\n\n</html>\n"}, "redirectURL": "", "headersSize": 247, "bodySize": 2439}, "cache": {}, "timings": {"dns": 0, "connect": 186, "blocked": 1, "send": 0, "wait": 253, "receive": 0}}, {"pageref": "page_7689", "startedDateTime": "2010-01-02T15:39:42.513+01:00", "time": 2405, "request": {"method": "GET", "url": "http://1.cuzillion.com/bin/resource.cgi?type=gif&sleep=2&n=1&t=1262443187", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Host", "value": "1.cuzillion.com"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows; U; Windows NT 6.0; en-US; rv:1.9.2b6pre) Gecko/20091230 Namoroka/3.6b6pre (.NET CLR 3.5.30729)"}, {"name": "Accept", "value": "image/png,image/*;q=0.8,*/*;q=0.5"}, {"name": "Accept-Language", "value": "en-us,en;q=0.5"}, {"name": "Accept-Encoding", "value": "gzip,deflate"}, {"name": "Accept-<PERSON><PERSON><PERSON>", "value": "ISO-8859-1,utf-8;q=0.7,*;q=0.7"}, {"name": "Keep-Alive", "value": "115"}, {"name": "Connection", "value": "keep-alive"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://stevesouders.com/cuzillion/?c0=bi1hfff2_0_f&c1=bi1hfff2_0_f&c2=bi1hfff2_0_f&c3=bi1hfff2_0_f&c4=bi1hfff2_0_f&c5=bi1hfff2_0_f&c6=bi1hfff2_0_f&c7=bi1hfff2_0_f&t=1258547264277"}], "queryString": [{"name": "n", "value": "1"}, {"name": "sleep", "value": "2"}, {"name": "t", "value": "1262443187"}, {"name": "type", "value": "gif"}], "headersSize": 606, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Date", "value": "Sat, 02 Jan 2010 14:39:48 GMT"}, {"name": "Server", "value": "Apache"}, {"name": "Expires", "value": "Mon, 01 Feb 2010 14:39:50 GMT"}, {"name": "Cache-Control", "value": "public, max-age=2592000"}, {"name": "Last-Modified", "value": "Sun, 15 Jan 2006 12:00:00 GMT"}, {"name": "Content-Length", "value": "1525"}, {"name": "Keep-Alive", "value": "timeout=2, max=100"}, {"name": "Connection", "value": "Keep-Alive"}, {"name": "Content-Type", "value": "image/gif"}], "content": {"size": 1525, "mimeType": "image/gif"}, "redirectURL": "", "headersSize": 301, "bodySize": 1525}, "cache": {}, "timings": {"dns": 0, "connect": 185, "blocked": 0, "send": 0, "wait": 2220, "receive": 0}}, {"pageref": "page_7689", "startedDateTime": "2010-01-02T15:39:42.515+01:00", "time": 2490, "request": {"method": "GET", "url": "http://1.cuzillion.com/bin/resource.cgi?type=gif&sleep=2&n=2&t=1262443187", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Host", "value": "1.cuzillion.com"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows; U; Windows NT 6.0; en-US; rv:1.9.2b6pre) Gecko/20091230 Namoroka/3.6b6pre (.NET CLR 3.5.30729)"}, {"name": "Accept", "value": "image/png,image/*;q=0.8,*/*;q=0.5"}, {"name": "Accept-Language", "value": "en-us,en;q=0.5"}, {"name": "Accept-Encoding", "value": "gzip,deflate"}, {"name": "Accept-<PERSON><PERSON><PERSON>", "value": "ISO-8859-1,utf-8;q=0.7,*;q=0.7"}, {"name": "Keep-Alive", "value": "115"}, {"name": "Connection", "value": "keep-alive"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://stevesouders.com/cuzillion/?c0=bi1hfff2_0_f&c1=bi1hfff2_0_f&c2=bi1hfff2_0_f&c3=bi1hfff2_0_f&c4=bi1hfff2_0_f&c5=bi1hfff2_0_f&c6=bi1hfff2_0_f&c7=bi1hfff2_0_f&t=1258547264277"}], "queryString": [{"name": "n", "value": "2"}, {"name": "sleep", "value": "2"}, {"name": "t", "value": "1262443187"}, {"name": "type", "value": "gif"}], "headersSize": 606, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Date", "value": "Sat, 02 Jan 2010 14:39:48 GMT"}, {"name": "Server", "value": "Apache"}, {"name": "Expires", "value": "Mon, 01 Feb 2010 14:39:50 GMT"}, {"name": "Cache-Control", "value": "public, max-age=2592000"}, {"name": "Last-Modified", "value": "Sun, 15 Jan 2006 12:00:00 GMT"}, {"name": "Keep-Alive", "value": "timeout=2, max=100"}, {"name": "Connection", "value": "Keep-Alive"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "Content-Type", "value": "image/gif"}], "content": {"size": 492, "mimeType": "image/gif"}, "redirectURL": "", "headersSize": 307, "bodySize": 492}, "cache": {}, "timings": {"dns": 0, "connect": 187, "blocked": 0, "send": 0, "wait": 2302, "receive": 1}}, {"pageref": "page_7689", "startedDateTime": "2010-01-02T15:39:42.517+01:00", "time": 2465, "request": {"method": "GET", "url": "http://1.cuzillion.com/bin/resource.cgi?type=gif&sleep=2&n=3&t=1262443187", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Host", "value": "1.cuzillion.com"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows; U; Windows NT 6.0; en-US; rv:1.9.2b6pre) Gecko/20091230 Namoroka/3.6b6pre (.NET CLR 3.5.30729)"}, {"name": "Accept", "value": "image/png,image/*;q=0.8,*/*;q=0.5"}, {"name": "Accept-Language", "value": "en-us,en;q=0.5"}, {"name": "Accept-Encoding", "value": "gzip,deflate"}, {"name": "Accept-<PERSON><PERSON><PERSON>", "value": "ISO-8859-1,utf-8;q=0.7,*;q=0.7"}, {"name": "Keep-Alive", "value": "115"}, {"name": "Connection", "value": "keep-alive"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://stevesouders.com/cuzillion/?c0=bi1hfff2_0_f&c1=bi1hfff2_0_f&c2=bi1hfff2_0_f&c3=bi1hfff2_0_f&c4=bi1hfff2_0_f&c5=bi1hfff2_0_f&c6=bi1hfff2_0_f&c7=bi1hfff2_0_f&t=1258547264277"}], "queryString": [{"name": "n", "value": "3"}, {"name": "sleep", "value": "2"}, {"name": "t", "value": "1262443187"}, {"name": "type", "value": "gif"}], "headersSize": 606, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Date", "value": "Sat, 02 Jan 2010 14:39:48 GMT"}, {"name": "Server", "value": "Apache"}, {"name": "Expires", "value": "Mon, 01 Feb 2010 14:39:50 GMT"}, {"name": "Cache-Control", "value": "public, max-age=2592000"}, {"name": "Last-Modified", "value": "Sun, 15 Jan 2006 12:00:00 GMT"}, {"name": "Content-Length", "value": "1076"}, {"name": "Keep-Alive", "value": "timeout=2, max=100"}, {"name": "Connection", "value": "Keep-Alive"}, {"name": "Content-Type", "value": "image/gif"}], "content": {"size": 1076, "mimeType": "image/gif"}, "redirectURL": "", "headersSize": 301, "bodySize": 1076}, "cache": {}, "timings": {"dns": 1, "connect": 189, "blocked": 1, "send": 0, "wait": 2274, "receive": 0}}, {"pageref": "page_7689", "startedDateTime": "2010-01-02T15:39:42.519+01:00", "time": 2446, "request": {"method": "GET", "url": "http://1.cuzillion.com/bin/resource.cgi?type=gif&sleep=2&n=4&t=1262443187", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Host", "value": "1.cuzillion.com"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows; U; Windows NT 6.0; en-US; rv:1.9.2b6pre) Gecko/20091230 Namoroka/3.6b6pre (.NET CLR 3.5.30729)"}, {"name": "Accept", "value": "image/png,image/*;q=0.8,*/*;q=0.5"}, {"name": "Accept-Language", "value": "en-us,en;q=0.5"}, {"name": "Accept-Encoding", "value": "gzip,deflate"}, {"name": "Accept-<PERSON><PERSON><PERSON>", "value": "ISO-8859-1,utf-8;q=0.7,*;q=0.7"}, {"name": "Keep-Alive", "value": "115"}, {"name": "Connection", "value": "keep-alive"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://stevesouders.com/cuzillion/?c0=bi1hfff2_0_f&c1=bi1hfff2_0_f&c2=bi1hfff2_0_f&c3=bi1hfff2_0_f&c4=bi1hfff2_0_f&c5=bi1hfff2_0_f&c6=bi1hfff2_0_f&c7=bi1hfff2_0_f&t=1258547264277"}], "queryString": [{"name": "n", "value": "4"}, {"name": "sleep", "value": "2"}, {"name": "t", "value": "1262443187"}, {"name": "type", "value": "gif"}], "headersSize": 606, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Date", "value": "Sat, 02 Jan 2010 14:39:48 GMT"}, {"name": "Server", "value": "Apache"}, {"name": "Expires", "value": "Mon, 01 Feb 2010 14:39:50 GMT"}, {"name": "Cache-Control", "value": "public, max-age=2592000"}, {"name": "Last-Modified", "value": "Sun, 15 Jan 2006 12:00:00 GMT"}, {"name": "Content-Length", "value": "492"}, {"name": "Keep-Alive", "value": "timeout=2, max=100"}, {"name": "Connection", "value": "Keep-Alive"}, {"name": "Content-Type", "value": "image/gif"}], "content": {"size": 492, "mimeType": "image/gif"}, "redirectURL": "", "headersSize": 300, "bodySize": 492}, "cache": {}, "timings": {"dns": 0, "connect": 189, "blocked": 1, "send": 0, "wait": 2256, "receive": 0}}, {"pageref": "page_7689", "startedDateTime": "2010-01-02T15:39:42.521+01:00", "time": 2454, "request": {"method": "GET", "url": "http://1.cuzillion.com/bin/resource.cgi?type=gif&sleep=2&n=5&t=1262443187", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Host", "value": "1.cuzillion.com"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows; U; Windows NT 6.0; en-US; rv:1.9.2b6pre) Gecko/20091230 Namoroka/3.6b6pre (.NET CLR 3.5.30729)"}, {"name": "Accept", "value": "image/png,image/*;q=0.8,*/*;q=0.5"}, {"name": "Accept-Language", "value": "en-us,en;q=0.5"}, {"name": "Accept-Encoding", "value": "gzip,deflate"}, {"name": "Accept-<PERSON><PERSON><PERSON>", "value": "ISO-8859-1,utf-8;q=0.7,*;q=0.7"}, {"name": "Keep-Alive", "value": "115"}, {"name": "Connection", "value": "keep-alive"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://stevesouders.com/cuzillion/?c0=bi1hfff2_0_f&c1=bi1hfff2_0_f&c2=bi1hfff2_0_f&c3=bi1hfff2_0_f&c4=bi1hfff2_0_f&c5=bi1hfff2_0_f&c6=bi1hfff2_0_f&c7=bi1hfff2_0_f&t=1258547264277"}], "queryString": [{"name": "n", "value": "5"}, {"name": "sleep", "value": "2"}, {"name": "t", "value": "1262443187"}, {"name": "type", "value": "gif"}], "headersSize": 606, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Date", "value": "Sat, 02 Jan 2010 14:39:48 GMT"}, {"name": "Server", "value": "Apache"}, {"name": "Expires", "value": "Mon, 01 Feb 2010 14:39:50 GMT"}, {"name": "Cache-Control", "value": "public, max-age=2592000"}, {"name": "Last-Modified", "value": "Sun, 15 Jan 2006 12:00:00 GMT"}, {"name": "Content-Length", "value": "1525"}, {"name": "Keep-Alive", "value": "timeout=2, max=100"}, {"name": "Connection", "value": "Keep-Alive"}, {"name": "Content-Type", "value": "image/gif"}], "content": {"size": 1525, "mimeType": "image/gif"}, "redirectURL": "", "headersSize": 301, "bodySize": 1525}, "cache": {}, "timings": {"dns": 0, "connect": 194, "blocked": 1, "send": 0, "wait": 2259, "receive": 0}}, {"pageref": "page_7689", "startedDateTime": "2010-01-02T15:39:42.523+01:00", "time": 2465, "request": {"method": "GET", "url": "http://1.cuzillion.com/bin/resource.cgi?type=gif&sleep=2&n=6&t=1262443187", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Host", "value": "1.cuzillion.com"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows; U; Windows NT 6.0; en-US; rv:1.9.2b6pre) Gecko/20091230 Namoroka/3.6b6pre (.NET CLR 3.5.30729)"}, {"name": "Accept", "value": "image/png,image/*;q=0.8,*/*;q=0.5"}, {"name": "Accept-Language", "value": "en-us,en;q=0.5"}, {"name": "Accept-Encoding", "value": "gzip,deflate"}, {"name": "Accept-<PERSON><PERSON><PERSON>", "value": "ISO-8859-1,utf-8;q=0.7,*;q=0.7"}, {"name": "Keep-Alive", "value": "115"}, {"name": "Connection", "value": "keep-alive"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://stevesouders.com/cuzillion/?c0=bi1hfff2_0_f&c1=bi1hfff2_0_f&c2=bi1hfff2_0_f&c3=bi1hfff2_0_f&c4=bi1hfff2_0_f&c5=bi1hfff2_0_f&c6=bi1hfff2_0_f&c7=bi1hfff2_0_f&t=1258547264277"}], "queryString": [{"name": "n", "value": "6"}, {"name": "sleep", "value": "2"}, {"name": "t", "value": "1262443187"}, {"name": "type", "value": "gif"}], "headersSize": 606, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Date", "value": "Sat, 02 Jan 2010 14:39:48 GMT"}, {"name": "Server", "value": "Apache"}, {"name": "Expires", "value": "Mon, 01 Feb 2010 14:39:50 GMT"}, {"name": "Cache-Control", "value": "public, max-age=2592000"}, {"name": "Last-Modified", "value": "Sun, 15 Jan 2006 12:00:00 GMT"}, {"name": "Content-Length", "value": "1525"}, {"name": "Keep-Alive", "value": "timeout=2, max=100"}, {"name": "Connection", "value": "Keep-Alive"}, {"name": "Content-Type", "value": "image/gif"}], "content": {"size": 1525, "mimeType": "image/gif"}, "redirectURL": "", "headersSize": 301, "bodySize": 1525}, "cache": {}, "timings": {"dns": 0, "connect": 194, "blocked": 1, "send": 0, "wait": 2270, "receive": 0}}, {"pageref": "page_7689", "startedDateTime": "2010-01-02T15:39:42.525+01:00", "time": 4610, "request": {"method": "GET", "url": "http://1.cuzillion.com/bin/resource.cgi?type=gif&sleep=2&n=7&t=1262443187", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Host", "value": "1.cuzillion.com"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows; U; Windows NT 6.0; en-US; rv:1.9.2b6pre) Gecko/20091230 Namoroka/3.6b6pre (.NET CLR 3.5.30729)"}, {"name": "Accept", "value": "image/png,image/*;q=0.8,*/*;q=0.5"}, {"name": "Accept-Language", "value": "en-us,en;q=0.5"}, {"name": "Accept-Encoding", "value": "gzip,deflate"}, {"name": "Accept-<PERSON><PERSON><PERSON>", "value": "ISO-8859-1,utf-8;q=0.7,*;q=0.7"}, {"name": "Keep-Alive", "value": "115"}, {"name": "Connection", "value": "keep-alive"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://stevesouders.com/cuzillion/?c0=bi1hfff2_0_f&c1=bi1hfff2_0_f&c2=bi1hfff2_0_f&c3=bi1hfff2_0_f&c4=bi1hfff2_0_f&c5=bi1hfff2_0_f&c6=bi1hfff2_0_f&c7=bi1hfff2_0_f&t=1258547264277"}], "queryString": [{"name": "n", "value": "7"}, {"name": "sleep", "value": "2"}, {"name": "t", "value": "1262443187"}, {"name": "type", "value": "gif"}], "headersSize": 606, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Date", "value": "Sat, 02 Jan 2010 14:39:50 GMT"}, {"name": "Server", "value": "Apache"}, {"name": "Expires", "value": "Mon, 01 Feb 2010 14:39:52 GMT"}, {"name": "Cache-Control", "value": "public, max-age=2592000"}, {"name": "Last-Modified", "value": "Sun, 15 Jan 2006 12:00:00 GMT"}, {"name": "Content-Length", "value": "1076"}, {"name": "Keep-Alive", "value": "timeout=2, max=99"}, {"name": "Connection", "value": "Keep-Alive"}, {"name": "Content-Type", "value": "image/gif"}], "content": {"size": 1076, "mimeType": "image/gif"}, "redirectURL": "", "headersSize": 300, "bodySize": 1076}, "cache": {}, "timings": {"dns": 0, "connect": 0, "blocked": 2394, "send": 0, "wait": 2216, "receive": 0}}, {"pageref": "page_7689", "startedDateTime": "2010-01-02T15:39:42.526+01:00", "time": 4652, "request": {"method": "GET", "url": "http://1.cuzillion.com/bin/resource.cgi?type=gif&sleep=2&n=8&t=1262443187", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Host", "value": "1.cuzillion.com"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows; U; Windows NT 6.0; en-US; rv:1.9.2b6pre) Gecko/20091230 Namoroka/3.6b6pre (.NET CLR 3.5.30729)"}, {"name": "Accept", "value": "image/png,image/*;q=0.8,*/*;q=0.5"}, {"name": "Accept-Language", "value": "en-us,en;q=0.5"}, {"name": "Accept-Encoding", "value": "gzip,deflate"}, {"name": "Accept-<PERSON><PERSON><PERSON>", "value": "ISO-8859-1,utf-8;q=0.7,*;q=0.7"}, {"name": "Keep-Alive", "value": "115"}, {"name": "Connection", "value": "keep-alive"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://stevesouders.com/cuzillion/?c0=bi1hfff2_0_f&c1=bi1hfff2_0_f&c2=bi1hfff2_0_f&c3=bi1hfff2_0_f&c4=bi1hfff2_0_f&c5=bi1hfff2_0_f&c6=bi1hfff2_0_f&c7=bi1hfff2_0_f&t=1258547264277"}], "queryString": [{"name": "n", "value": "8"}, {"name": "sleep", "value": "2"}, {"name": "t", "value": "1262443187"}, {"name": "type", "value": "gif"}], "headersSize": 606, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Date", "value": "Sat, 02 Jan 2010 14:39:50 GMT"}, {"name": "Server", "value": "Apache"}, {"name": "Expires", "value": "Mon, 01 Feb 2010 14:39:52 GMT"}, {"name": "Cache-Control", "value": "public, max-age=2592000"}, {"name": "Last-Modified", "value": "Sun, 15 Jan 2006 12:00:00 GMT"}, {"name": "Content-Length", "value": "1076"}, {"name": "Keep-Alive", "value": "timeout=2, max=99"}, {"name": "Connection", "value": "Keep-Alive"}, {"name": "Content-Type", "value": "image/gif"}], "content": {"size": 1076, "mimeType": "image/gif"}, "redirectURL": "", "headersSize": 300, "bodySize": 1076}, "cache": {}, "timings": {"dns": 0, "connect": 0, "blocked": 2439, "send": 0, "wait": 2213, "receive": 0}}, {"pageref": "page_12043", "startedDateTime": "2010-01-02T16:40:21.935+01:00", "time": 438, "request": {"method": "GET", "url": "http://stevesouders.com/cuzillion/?c0=bi1hfff2_0_f&c1=bi1hfff2_0_f&c2=bi1hfff2_0_f&c3=bi1hfff2_0_f&c4=bi1hfff2_0_f&c5=bi1hfff2_0_f&c6=bi1hfff2_0_f&c7=bi1hfff2_0_f&t=1258547264277", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Host", "value": "stevesouders.com"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows; U; Windows NT 6.0; en-US; rv:1.9.2b6pre) Gecko/20091230 Namoroka/3.6b6pre (.NET CLR 3.5.30729)"}, {"name": "Accept", "value": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"}, {"name": "Accept-Language", "value": "en-us,en;q=0.5"}, {"name": "Accept-Encoding", "value": "gzip,deflate"}, {"name": "Accept-<PERSON><PERSON><PERSON>", "value": "ISO-8859-1,utf-8;q=0.7,*;q=0.7"}, {"name": "Keep-Alive", "value": "115"}, {"name": "Connection", "value": "keep-alive"}], "queryString": [{"name": "c0", "value": "bi1hfff2_0_f"}, {"name": "c1", "value": "bi1hfff2_0_f"}, {"name": "c2", "value": "bi1hfff2_0_f"}, {"name": "c3", "value": "bi1hfff2_0_f"}, {"name": "c4", "value": "bi1hfff2_0_f"}, {"name": "c5", "value": "bi1hfff2_0_f"}, {"name": "c6", "value": "bi1hfff2_0_f"}, {"name": "c7", "value": "bi1hfff2_0_f"}, {"name": "t", "value": "1258547264277"}], "headersSize": 552, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Date", "value": "Sat, 02 Jan 2010 15:40:27 GMT"}, {"name": "Server", "value": "Apache"}, {"name": "X-Powered-By", "value": "PHP/5.2.3"}, {"name": "Vary", "value": "Accept-Encoding"}, {"name": "Content-Encoding", "value": "gzip"}, {"name": "Content-Length", "value": "2439"}, {"name": "Keep-Alive", "value": "timeout=2, max=100"}, {"name": "Connection", "value": "Keep-Alive"}, {"name": "Content-Type", "value": "text/html"}], "content": {"size": 11576, "mimeType": "text/html", "text": "\n<script>\nvar gTop = Number(new Date());\nvar gScriptMsg = \"\";\nfunction cuz_addHandler(elem, sType, fn, capture) {\n    capture = (capture) ? true : false;\n    if (elem.addEventListener) {\n        elem.addEventListener(sType, fn, capture);\n    }\n    else if (elem.attachEvent) {\n        elem.attachEvent(\"on\" + sType, fn);\n    }\n    else {\n        // Netscape 4\n        if ( elem[\"on\" + sType] ) {\n            // Do nothing - we don't want to overwrite an existing handler.\n        }\n        else {\n            elem[\"on\" + sType] = fn;\n        }\n    }\n}\nfunction doOnload() {\n\tvar end = Number(new Date());\n    document.getElementById('loadtime').innerHTML = 'page load time: ' + (end - gTop) + ' ms';\n\tif ( gScriptMsg && document.getElementById('loadedscripts') ) {\n\t\tdocument.getElementById('loadedscripts').innerHTML += gScriptMsg;\n\t}\n}\ncuz_addHandler(window, 'load', doOnload);\nvar gbEnabled = false;\nfunction enableEdit() {\n\tif ( gbEnabled ) return;\n\tgbEnabled = true;\n\taddStylesheet('cuzillion.1.1.css');\n\taddScript('cuzillion.1.5.js');\n}\nfunction addStylesheet(url) {\n\tvar stylesheet = document.createElement('link');\n\tstylesheet.rel = 'stylesheet';\n\tstylesheet.type = 'text/css';\n\tstylesheet.href =  url;\n\tdocument.getElementsByTagName('head')[0].appendChild(stylesheet);\n}\nfunction addScript(url) {\n\tvar script = document.createElement('script');\n\tscript.src = url;\n\tdocument.getElementsByTagName('head')[0].appendChild(script);\n}\nfunction scriptSleepOnload(sUrl) {\n\tvar now = Number(new Date());\n\tvar msg = \"<nobr>\" + (now - gTop) + \"ms: \\\"\" + sUrl + \"\\\" done</nobr>\\n\";\n\tif ( document.getElementById('loadedscripts') ) {\n\t\tdocument.getElementById('loadedscripts').innerHTML += msg;\n\t}\n\telse {\n\t\tgScriptMsg += msg;\n\t}\n}\nfunction reloadPage(url) {\n\tdocument.body.innerHTML = '';\n\tdocument.location = url;\n}\nfunction cleanText(sText) {\n    return sText.replace(/<.*?>/g, '');\n}\n</script>\n<html>\n<head>\n<title>Cuzillion</title>\n<link rel=\"icon\" href=\"favicon.ico\" type=\"image/x-icon\">\n<!--\nCopyright 2008 Google Inc.\n\nLicensed under the Apache License, Version 2.0 (the \"License\");\nyou may not use this file except in compliance with the License.\nYou may obtain a copy of the License at\n\n     http://www.apache.org/licenses/LICENSE-2.0\n\nUnless required by applicable law or agreed to in writing, software\ndistributed under the License is distributed on an \"AS IS\" BASIS,\nWITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\nSee the License for the specific language governing permissions and\nlimitations under the License.\n-->\n</head>\n\n<body style='margin: 0px; padding: 0px; font-family: \"Trebuchet MS\", \"Bitstream Vera Serif\", Utopia, \"Times New Roman\", times, serif;'>\n\n<div style=\"background: #333; color: white; padding: 8px;\">\n  <div style=\"float:right; margin-top: 2px;\">\n    <a href=\"help.php#examples\" style=\"color: white; font-size: 0.9em; text-decoration: none;\">Examples</a>&nbsp;|&nbsp;<a href=\"help.php\" style=\"color: white; font-size: 0.9em; text-decoration: none;\">Help</a><br><a href=\"http://stevesouders.com/\" style=\"color: white; font-size: 0.9em; text-decoration: none;\">stevesouders.com</a>\n  </div>\n  <font style=\"font-size: 2em; font-weight: bold; margin-right: 10px;\"><a href=\".\" style=\"color:white; text-decoration: none;\"><img border=0 src=\"logo-32x32.gif\">&nbsp;Cuzillion</a></font><i>'cuz there are a zillion pages to check</i>\n</div>\n\n<div id=content style=\"margin: 8px;\">\n\n\n<!-- begin resources for inbody -->\n<!-- image<p style='margin: 0 4px 4px 12px; font-size: 0.8em;'> on domain1 with a 2 second delay using HTML tags -->\n<img src='http://1.cuzillion.com/bin/resource.cgi?type=gif&sleep=2&n=1&t=1262446827'>\n\n<!-- image<p style='margin: 0 4px 4px 12px; font-size: 0.8em;'> on domain1 with a 2 second delay using HTML tags -->\n<img src='http://1.cuzillion.com/bin/resource.cgi?type=gif&sleep=2&n=2&t=1262446827'>\n\n<!-- image<p style='margin: 0 4px 4px 12px; font-size: 0.8em;'> on domain1 with a 2 second delay using HTML tags -->\n<img src='http://1.cuzillion.com/bin/resource.cgi?type=gif&sleep=2&n=3&t=1262446827'>\n\n<!-- image<p style='margin: 0 4px 4px 12px; font-size: 0.8em;'> on domain1 with a 2 second delay using HTML tags -->\n<img src='http://1.cuzillion.com/bin/resource.cgi?type=gif&sleep=2&n=4&t=1262446827'>\n\n<!-- image<p style='margin: 0 4px 4px 12px; font-size: 0.8em;'> on domain1 with a 2 second delay using HTML tags -->\n<img src='http://1.cuzillion.com/bin/resource.cgi?type=gif&sleep=2&n=5&t=1262446827'>\n\n<!-- image<p style='margin: 0 4px 4px 12px; font-size: 0.8em;'> on domain1 with a 2 second delay using HTML tags -->\n<img src='http://1.cuzillion.com/bin/resource.cgi?type=gif&sleep=2&n=6&t=1262446827'>\n\n<!-- image<p style='margin: 0 4px 4px 12px; font-size: 0.8em;'> on domain1 with a 2 second delay using HTML tags -->\n<img src='http://1.cuzillion.com/bin/resource.cgi?type=gif&sleep=2&n=7&t=1262446827'>\n\n<!-- image<p style='margin: 0 4px 4px 12px; font-size: 0.8em;'> on domain1 with a 2 second delay using HTML tags -->\n<img src='http://1.cuzillion.com/bin/resource.cgi?type=gif&sleep=2&n=8&t=1262446827'>\n\n<!-- end resources for inbody -->\n\n<div id=floattray style=\"float: left; width: 170px; margin-right: 30px;\">\n  <div id=step1text style=\"text-align: left; margin: 0 0 4px 4px; height: 50px; padding-top: 12px;\"></div>\n  <div id=comptray>\n  &nbsp;\n  </div>\n</div>\n\n<div id=pageavatar style=\"float: left; width: 310px; margin-right: 30px;\">\n  <div id=step2text style=\"text-align: left; margin: 0 0 4px 4px; height: 50px; padding-top: 12px;\"></div>\n  <div style=\"background: #CCC; border: 1px solid black; \">\n    <code style=\"font-size: 1.2em; font-weight: bold; color: #666666; display: block;\">&lt;HTML&gt;</code>\n    <code style=\"font-size: 1.2em; font-weight: bold; color: #666666; display: block;\">&lt;HEAD&gt;</code>\n    <div class=\"drop\" style=\"border: 1px solid #EEE; background: #EEE; padding: 12px 0 12px 0; width: 300px; margin: 0 0 0 4px;\">\n\t  <ul style=\"margin: 0; padding: 0;\" id=inhead></ul>\n\t  <div id=inheadTarget></div>\n\t</div>\n    <code style=\"font-size: 1.2em; font-weight: bold; color: #666666; display: block;\">&lt;/HEAD&gt;</code>\n    <code style=\"font-size: 1.2em; font-weight: bold; color: #666666; display: block;\">&lt;BODY&gt;</code>\n    <div class=\"drop\" style=\"border: 1px solid #EEE; background: #EEE; padding: 12px 0 12px 0; width: 300px; margin: 0 0 0 4px;\">\n\t  <ul style=\"margin: 0; padding: 0;\" id=inbody><li onclick='enableEdit()' id='acomp1' class='sortitem image' style='cursor: move; list-style: none; border-width: 2px; border-style: solid; border-color: #555; margin: 4px;'><div id=acomp1Div class='component image' style='padding: 2px; font-family: Arial; text-align: center; display: block; text-decoration: none; color: white; background: #000080; text-align: left;'><span>image<p style='margin: 0 4px 4px 12px; font-size: 0.8em;'> on domain1 with a 2 second delay using HTML tags</span></div>\n<li onclick='enableEdit()' id='acomp2' class='sortitem image' style='cursor: move; list-style: none; border-width: 2px; border-style: solid; border-color: #555; margin: 4px;'><div id=acomp2Div class='component image' style='padding: 2px; font-family: Arial; text-align: center; display: block; text-decoration: none; color: white; background: #000080; text-align: left;'><span>image<p style='margin: 0 4px 4px 12px; font-size: 0.8em;'> on domain1 with a 2 second delay using HTML tags</span></div>\n<li onclick='enableEdit()' id='acomp3' class='sortitem image' style='cursor: move; list-style: none; border-width: 2px; border-style: solid; border-color: #555; margin: 4px;'><div id=acomp3Div class='component image' style='padding: 2px; font-family: Arial; text-align: center; display: block; text-decoration: none; color: white; background: #000080; text-align: left;'><span>image<p style='margin: 0 4px 4px 12px; font-size: 0.8em;'> on domain1 with a 2 second delay using HTML tags</span></div>\n<li onclick='enableEdit()' id='acomp4' class='sortitem image' style='cursor: move; list-style: none; border-width: 2px; border-style: solid; border-color: #555; margin: 4px;'><div id=acomp4Div class='component image' style='padding: 2px; font-family: Arial; text-align: center; display: block; text-decoration: none; color: white; background: #000080; text-align: left;'><span>image<p style='margin: 0 4px 4px 12px; font-size: 0.8em;'> on domain1 with a 2 second delay using HTML tags</span></div>\n<li onclick='enableEdit()' id='acomp5' class='sortitem image' style='cursor: move; list-style: none; border-width: 2px; border-style: solid; border-color: #555; margin: 4px;'><div id=acomp5Div class='component image' style='padding: 2px; font-family: Arial; text-align: center; display: block; text-decoration: none; color: white; background: #000080; text-align: left;'><span>image<p style='margin: 0 4px 4px 12px; font-size: 0.8em;'> on domain1 with a 2 second delay using HTML tags</span></div>\n<li onclick='enableEdit()' id='acomp6' class='sortitem image' style='cursor: move; list-style: none; border-width: 2px; border-style: solid; border-color: #555; margin: 4px;'><div id=acomp6Div class='component image' style='padding: 2px; font-family: Arial; text-align: center; display: block; text-decoration: none; color: white; background: #000080; text-align: left;'><span>image<p style='margin: 0 4px 4px 12px; font-size: 0.8em;'> on domain1 with a 2 second delay using HTML tags</span></div>\n<li onclick='enableEdit()' id='acomp7' class='sortitem image' style='cursor: move; list-style: none; border-width: 2px; border-style: solid; border-color: #555; margin: 4px;'><div id=acomp7Div class='component image' style='padding: 2px; font-family: Arial; text-align: center; display: block; text-decoration: none; color: white; background: #000080; text-align: left;'><span>image<p style='margin: 0 4px 4px 12px; font-size: 0.8em;'> on domain1 with a 2 second delay using HTML tags</span></div>\n<li onclick='enableEdit()' id='acomp8' class='sortitem image' style='cursor: move; list-style: none; border-width: 2px; border-style: solid; border-color: #555; margin: 4px;'><div id=acomp8Div class='component image' style='padding: 2px; font-family: Arial; text-align: center; display: block; text-decoration: none; color: white; background: #000080; text-align: left;'><span>image<p style='margin: 0 4px 4px 12px; font-size: 0.8em;'> on domain1 with a 2 second delay using HTML tags</span></div>\n</ul>\n\t  <div id=inbodyTarget></div>\n\t</div>\n    <code style=\"font-size: 1.2em; font-weight: bold; color: #666666; display: block;\">&lt;/BODY&gt;</code>\n    <code style=\"font-size: 1.2em; font-weight: bold; color: #666666; display: block;\">&lt;/HTML&gt;</code>\n  </div>\n  <div id=loadtime style=\"text-align: left; margin-top: 10px;\"></div>\n  <div id=loadedscripts style=\"text-align: left; margin-top: 10px; width: 300px; font-size: 0.9em;\"></div>\n</div> <!-- end pageavatar -->\n\n<div style=\"position: absolute; left: 560px;\">\n  <div id=step3text style=\"text-align: left; margin: 0 0 4px 4px; height: 50px; padding-top: 12px;\"></div>\n  <div id=pagesubmit style=\"text-align: left;\">\n<nobr>\n<input type=button value=\"Edit\" onclick=\"enableEdit()\">&nbsp;&nbsp;\n<input type=button value=\"Reload\" onclick=\"reloadPage('/cuzillion/?c0=bi1hfff2_0_f&c1=bi1hfff2_0_f&c2=bi1hfff2_0_f&c3=bi1hfff2_0_f&c4=bi1hfff2_0_f&c5=bi1hfff2_0_f&c6=bi1hfff2_0_f&c7=bi1hfff2_0_f&t=1262446827')\">&nbsp;&nbsp;\n<input type=button value=\"Clear\" onclick=\"document.location='.'\">&nbsp;&nbsp;\n</nobr>\n </div>\n</div>\n\n<div style=\"clear: both;\">\n</div>\n\n</div> <!-- content -->\n\n\n\n</body>\n\n</html>\n"}, "redirectURL": "", "headersSize": 247, "bodySize": 2439}, "cache": {}, "timings": {"dns": 0, "connect": 183, "blocked": 0, "send": 0, "wait": 255, "receive": 0}}, {"pageref": "page_12043", "startedDateTime": "2010-01-02T16:40:22.403+01:00", "time": 200, "request": {"method": "GET", "url": "http://stevesouders.com/cuzillion/logo-32x32.gif", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Host", "value": "stevesouders.com"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows; U; Windows NT 6.0; en-US; rv:1.9.2b6pre) Gecko/20091230 Namoroka/3.6b6pre (.NET CLR 3.5.30729)"}, {"name": "Accept", "value": "image/png,image/*;q=0.8,*/*;q=0.5"}, {"name": "Accept-Language", "value": "en-us,en;q=0.5"}, {"name": "Accept-Encoding", "value": "gzip,deflate"}, {"name": "Accept-<PERSON><PERSON><PERSON>", "value": "ISO-8859-1,utf-8;q=0.7,*;q=0.7"}, {"name": "Keep-Alive", "value": "115"}, {"name": "Connection", "value": "keep-alive"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://stevesouders.com/cuzillion/?c0=bi1hfff2_0_f&c1=bi1hfff2_0_f&c2=bi1hfff2_0_f&c3=bi1hfff2_0_f&c4=bi1hfff2_0_f&c5=bi1hfff2_0_f&c6=bi1hfff2_0_f&c7=bi1hfff2_0_f&t=1258547264277"}], "queryString": [], "headersSize": 581, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Date", "value": "Sat, 02 Jan 2010 15:40:27 GMT"}, {"name": "Server", "value": "Apache"}, {"name": "Last-Modified", "value": "Mon, 16 Nov 2009 20:19:20 GMT"}, {"name": "Accept-Ranges", "value": "bytes"}, {"name": "Content-Length", "value": "1057"}, {"name": "Cache-Control", "value": "max-age=315360000"}, {"name": "Expires", "value": "<PERSON><PERSON>, 31 Dec 2019 15:40:27 GMT"}, {"name": "Keep-Alive", "value": "timeout=2, max=99"}, {"name": "Connection", "value": "Keep-Alive"}, {"name": "Content-Type", "value": "image/gif"}], "content": {"size": 1057, "mimeType": "image/gif"}, "redirectURL": "", "headersSize": 316, "bodySize": 1057}, "cache": {}, "timings": {"dns": 0, "connect": 0, "blocked": 0, "send": 0, "wait": 192, "receive": 8}}, {"pageref": "page_12043", "startedDateTime": "2010-01-02T16:40:22.403+01:00", "time": 2437, "request": {"method": "GET", "url": "http://1.cuzillion.com/bin/resource.cgi?type=gif&sleep=2&n=1&t=1262446827", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Host", "value": "1.cuzillion.com"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows; U; Windows NT 6.0; en-US; rv:1.9.2b6pre) Gecko/20091230 Namoroka/3.6b6pre (.NET CLR 3.5.30729)"}, {"name": "Accept", "value": "image/png,image/*;q=0.8,*/*;q=0.5"}, {"name": "Accept-Language", "value": "en-us,en;q=0.5"}, {"name": "Accept-Encoding", "value": "gzip,deflate"}, {"name": "Accept-<PERSON><PERSON><PERSON>", "value": "ISO-8859-1,utf-8;q=0.7,*;q=0.7"}, {"name": "Keep-Alive", "value": "115"}, {"name": "Connection", "value": "keep-alive"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://stevesouders.com/cuzillion/?c0=bi1hfff2_0_f&c1=bi1hfff2_0_f&c2=bi1hfff2_0_f&c3=bi1hfff2_0_f&c4=bi1hfff2_0_f&c5=bi1hfff2_0_f&c6=bi1hfff2_0_f&c7=bi1hfff2_0_f&t=1258547264277"}], "queryString": [{"name": "n", "value": "1"}, {"name": "sleep", "value": "2"}, {"name": "t", "value": "1262446827"}, {"name": "type", "value": "gif"}], "headersSize": 606, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Date", "value": "Sat, 02 Jan 2010 15:40:28 GMT"}, {"name": "Server", "value": "Apache"}, {"name": "Expires", "value": "Mon, 01 Feb 2010 15:40:30 GMT"}, {"name": "Cache-Control", "value": "public, max-age=2592000"}, {"name": "Last-Modified", "value": "Sun, 15 Jan 2006 12:00:00 GMT"}, {"name": "Content-Length", "value": "1076"}, {"name": "Keep-Alive", "value": "timeout=2, max=100"}, {"name": "Connection", "value": "Keep-Alive"}, {"name": "Content-Type", "value": "image/gif"}], "content": {"size": 1076, "mimeType": "image/gif"}, "redirectURL": "", "headersSize": 301, "bodySize": 1076}, "cache": {}, "timings": {"dns": 2, "connect": 188, "blocked": 0, "send": 0, "wait": 2247, "receive": 0}}, {"pageref": "page_12043", "startedDateTime": "2010-01-02T16:40:22.405+01:00", "time": 2418, "request": {"method": "GET", "url": "http://1.cuzillion.com/bin/resource.cgi?type=gif&sleep=2&n=2&t=1262446827", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Host", "value": "1.cuzillion.com"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows; U; Windows NT 6.0; en-US; rv:1.9.2b6pre) Gecko/20091230 Namoroka/3.6b6pre (.NET CLR 3.5.30729)"}, {"name": "Accept", "value": "image/png,image/*;q=0.8,*/*;q=0.5"}, {"name": "Accept-Language", "value": "en-us,en;q=0.5"}, {"name": "Accept-Encoding", "value": "gzip,deflate"}, {"name": "Accept-<PERSON><PERSON><PERSON>", "value": "ISO-8859-1,utf-8;q=0.7,*;q=0.7"}, {"name": "Keep-Alive", "value": "115"}, {"name": "Connection", "value": "keep-alive"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://stevesouders.com/cuzillion/?c0=bi1hfff2_0_f&c1=bi1hfff2_0_f&c2=bi1hfff2_0_f&c3=bi1hfff2_0_f&c4=bi1hfff2_0_f&c5=bi1hfff2_0_f&c6=bi1hfff2_0_f&c7=bi1hfff2_0_f&t=1258547264277"}], "queryString": [{"name": "n", "value": "2"}, {"name": "sleep", "value": "2"}, {"name": "t", "value": "1262446827"}, {"name": "type", "value": "gif"}], "headersSize": 606, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Date", "value": "Sat, 02 Jan 2010 15:40:28 GMT"}, {"name": "Server", "value": "Apache"}, {"name": "Expires", "value": "Mon, 01 Feb 2010 15:40:30 GMT"}, {"name": "Cache-Control", "value": "public, max-age=2592000"}, {"name": "Last-Modified", "value": "Sun, 15 Jan 2006 12:00:00 GMT"}, {"name": "Content-Length", "value": "492"}, {"name": "Keep-Alive", "value": "timeout=2, max=100"}, {"name": "Connection", "value": "Keep-Alive"}, {"name": "Content-Type", "value": "image/gif"}], "content": {"size": 492, "mimeType": "image/gif"}, "redirectURL": "", "headersSize": 300, "bodySize": 492}, "cache": {}, "timings": {"dns": 0, "connect": 190, "blocked": 3, "send": 0, "wait": 2225, "receive": 0}}, {"pageref": "page_12043", "startedDateTime": "2010-01-02T16:40:22.405+01:00", "time": 4633, "request": {"method": "GET", "url": "http://1.cuzillion.com/bin/resource.cgi?type=gif&sleep=2&n=3&t=1262446827", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Host", "value": "1.cuzillion.com"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows; U; Windows NT 6.0; en-US; rv:1.9.2b6pre) Gecko/20091230 Namoroka/3.6b6pre (.NET CLR 3.5.30729)"}, {"name": "Accept", "value": "image/png,image/*;q=0.8,*/*;q=0.5"}, {"name": "Accept-Language", "value": "en-us,en;q=0.5"}, {"name": "Accept-Encoding", "value": "gzip,deflate"}, {"name": "Accept-<PERSON><PERSON><PERSON>", "value": "ISO-8859-1,utf-8;q=0.7,*;q=0.7"}, {"name": "Keep-Alive", "value": "115"}, {"name": "Connection", "value": "keep-alive"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://stevesouders.com/cuzillion/?c0=bi1hfff2_0_f&c1=bi1hfff2_0_f&c2=bi1hfff2_0_f&c3=bi1hfff2_0_f&c4=bi1hfff2_0_f&c5=bi1hfff2_0_f&c6=bi1hfff2_0_f&c7=bi1hfff2_0_f&t=1258547264277"}], "queryString": [{"name": "n", "value": "3"}, {"name": "sleep", "value": "2"}, {"name": "t", "value": "1262446827"}, {"name": "type", "value": "gif"}], "headersSize": 606, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Date", "value": "Sat, 02 Jan 2010 15:40:30 GMT"}, {"name": "Server", "value": "Apache"}, {"name": "Expires", "value": "Mon, 01 Feb 2010 15:40:32 GMT"}, {"name": "Cache-Control", "value": "public, max-age=2592000"}, {"name": "Last-Modified", "value": "Sun, 15 Jan 2006 12:00:00 GMT"}, {"name": "Keep-Alive", "value": "timeout=2, max=99"}, {"name": "Connection", "value": "Keep-Alive"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "Content-Type", "value": "image/gif"}], "content": {"size": 1334, "mimeType": "image/gif"}, "redirectURL": "", "headersSize": 306, "bodySize": 1334}, "cache": {}, "timings": {"dns": 0, "connect": 0, "blocked": 2418, "send": 0, "wait": 2215, "receive": 0}}, {"pageref": "page_12043", "startedDateTime": "2010-01-02T16:40:22.408+01:00", "time": 4645, "request": {"method": "GET", "url": "http://1.cuzillion.com/bin/resource.cgi?type=gif&sleep=2&n=4&t=1262446827", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Host", "value": "1.cuzillion.com"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows; U; Windows NT 6.0; en-US; rv:1.9.2b6pre) Gecko/20091230 Namoroka/3.6b6pre (.NET CLR 3.5.30729)"}, {"name": "Accept", "value": "image/png,image/*;q=0.8,*/*;q=0.5"}, {"name": "Accept-Language", "value": "en-us,en;q=0.5"}, {"name": "Accept-Encoding", "value": "gzip,deflate"}, {"name": "Accept-<PERSON><PERSON><PERSON>", "value": "ISO-8859-1,utf-8;q=0.7,*;q=0.7"}, {"name": "Keep-Alive", "value": "115"}, {"name": "Connection", "value": "keep-alive"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://stevesouders.com/cuzillion/?c0=bi1hfff2_0_f&c1=bi1hfff2_0_f&c2=bi1hfff2_0_f&c3=bi1hfff2_0_f&c4=bi1hfff2_0_f&c5=bi1hfff2_0_f&c6=bi1hfff2_0_f&c7=bi1hfff2_0_f&t=1258547264277"}], "queryString": [{"name": "n", "value": "4"}, {"name": "sleep", "value": "2"}, {"name": "t", "value": "1262446827"}, {"name": "type", "value": "gif"}], "headersSize": 606, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Date", "value": "Sat, 02 Jan 2010 15:40:30 GMT"}, {"name": "Server", "value": "Apache"}, {"name": "Expires", "value": "Mon, 01 Feb 2010 15:40:32 GMT"}, {"name": "Cache-Control", "value": "public, max-age=2592000"}, {"name": "Last-Modified", "value": "Sun, 15 Jan 2006 12:00:00 GMT"}, {"name": "Content-Length", "value": "1525"}, {"name": "Keep-Alive", "value": "timeout=2, max=99"}, {"name": "Connection", "value": "Keep-Alive"}, {"name": "Content-Type", "value": "image/gif"}], "content": {"size": 1525, "mimeType": "image/gif"}, "redirectURL": "", "headersSize": 300, "bodySize": 1525}, "cache": {}, "timings": {"dns": 0, "connect": 0, "blocked": 2435, "send": 0, "wait": 2210, "receive": 0}}, {"pageref": "page_12043", "startedDateTime": "2010-01-02T16:40:22.408+01:00", "time": 6845, "request": {"method": "GET", "url": "http://1.cuzillion.com/bin/resource.cgi?type=gif&sleep=2&n=5&t=1262446827", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Host", "value": "1.cuzillion.com"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows; U; Windows NT 6.0; en-US; rv:1.9.2b6pre) Gecko/20091230 Namoroka/3.6b6pre (.NET CLR 3.5.30729)"}, {"name": "Accept", "value": "image/png,image/*;q=0.8,*/*;q=0.5"}, {"name": "Accept-Language", "value": "en-us,en;q=0.5"}, {"name": "Accept-Encoding", "value": "gzip,deflate"}, {"name": "Accept-<PERSON><PERSON><PERSON>", "value": "ISO-8859-1,utf-8;q=0.7,*;q=0.7"}, {"name": "Keep-Alive", "value": "115"}, {"name": "Connection", "value": "keep-alive"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://stevesouders.com/cuzillion/?c0=bi1hfff2_0_f&c1=bi1hfff2_0_f&c2=bi1hfff2_0_f&c3=bi1hfff2_0_f&c4=bi1hfff2_0_f&c5=bi1hfff2_0_f&c6=bi1hfff2_0_f&c7=bi1hfff2_0_f&t=1258547264277"}], "queryString": [{"name": "n", "value": "5"}, {"name": "sleep", "value": "2"}, {"name": "t", "value": "1262446827"}, {"name": "type", "value": "gif"}], "headersSize": 606, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Date", "value": "Sat, 02 Jan 2010 15:40:32 GMT"}, {"name": "Server", "value": "Apache"}, {"name": "Expires", "value": "Mon, 01 Feb 2010 15:40:34 GMT"}, {"name": "Cache-Control", "value": "public, max-age=2592000"}, {"name": "Last-Modified", "value": "Sun, 15 Jan 2006 12:00:00 GMT"}, {"name": "Content-Length", "value": "1076"}, {"name": "Keep-Alive", "value": "timeout=2, max=98"}, {"name": "Connection", "value": "Keep-Alive"}, {"name": "Content-Type", "value": "image/gif"}], "content": {"size": 1076, "mimeType": "image/gif"}, "redirectURL": "", "headersSize": 300, "bodySize": 1076}, "cache": {}, "timings": {"dns": 0, "connect": 0, "blocked": 4630, "send": 0, "wait": 2215, "receive": 0}}, {"pageref": "page_12043", "startedDateTime": "2010-01-02T16:40:22.410+01:00", "time": 6853, "request": {"method": "GET", "url": "http://1.cuzillion.com/bin/resource.cgi?type=gif&sleep=2&n=6&t=1262446827", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Host", "value": "1.cuzillion.com"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows; U; Windows NT 6.0; en-US; rv:1.9.2b6pre) Gecko/20091230 Namoroka/3.6b6pre (.NET CLR 3.5.30729)"}, {"name": "Accept", "value": "image/png,image/*;q=0.8,*/*;q=0.5"}, {"name": "Accept-Language", "value": "en-us,en;q=0.5"}, {"name": "Accept-Encoding", "value": "gzip,deflate"}, {"name": "Accept-<PERSON><PERSON><PERSON>", "value": "ISO-8859-1,utf-8;q=0.7,*;q=0.7"}, {"name": "Keep-Alive", "value": "115"}, {"name": "Connection", "value": "keep-alive"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://stevesouders.com/cuzillion/?c0=bi1hfff2_0_f&c1=bi1hfff2_0_f&c2=bi1hfff2_0_f&c3=bi1hfff2_0_f&c4=bi1hfff2_0_f&c5=bi1hfff2_0_f&c6=bi1hfff2_0_f&c7=bi1hfff2_0_f&t=1258547264277"}], "queryString": [{"name": "n", "value": "6"}, {"name": "sleep", "value": "2"}, {"name": "t", "value": "1262446827"}, {"name": "type", "value": "gif"}], "headersSize": 606, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Date", "value": "Sat, 02 Jan 2010 15:40:32 GMT"}, {"name": "Server", "value": "Apache"}, {"name": "Expires", "value": "Mon, 01 Feb 2010 15:40:34 GMT"}, {"name": "Cache-Control", "value": "public, max-age=2592000"}, {"name": "Last-Modified", "value": "Sun, 15 Jan 2006 12:00:00 GMT"}, {"name": "Content-Length", "value": "1334"}, {"name": "Keep-Alive", "value": "timeout=2, max=98"}, {"name": "Connection", "value": "Keep-Alive"}, {"name": "Content-Type", "value": "image/gif"}], "content": {"size": 1334, "mimeType": "image/gif"}, "redirectURL": "", "headersSize": 300, "bodySize": 1334}, "cache": {}, "timings": {"dns": 0, "connect": 0, "blocked": 4643, "send": 0, "wait": 2210, "receive": 0}}, {"pageref": "page_12043", "startedDateTime": "2010-01-02T16:40:22.410+01:00", "time": 9055, "request": {"method": "GET", "url": "http://1.cuzillion.com/bin/resource.cgi?type=gif&sleep=2&n=7&t=1262446827", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Host", "value": "1.cuzillion.com"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows; U; Windows NT 6.0; en-US; rv:1.9.2b6pre) Gecko/20091230 Namoroka/3.6b6pre (.NET CLR 3.5.30729)"}, {"name": "Accept", "value": "image/png,image/*;q=0.8,*/*;q=0.5"}, {"name": "Accept-Language", "value": "en-us,en;q=0.5"}, {"name": "Accept-Encoding", "value": "gzip,deflate"}, {"name": "Accept-<PERSON><PERSON><PERSON>", "value": "ISO-8859-1,utf-8;q=0.7,*;q=0.7"}, {"name": "Keep-Alive", "value": "115"}, {"name": "Connection", "value": "keep-alive"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://stevesouders.com/cuzillion/?c0=bi1hfff2_0_f&c1=bi1hfff2_0_f&c2=bi1hfff2_0_f&c3=bi1hfff2_0_f&c4=bi1hfff2_0_f&c5=bi1hfff2_0_f&c6=bi1hfff2_0_f&c7=bi1hfff2_0_f&t=1258547264277"}], "queryString": [{"name": "n", "value": "7"}, {"name": "sleep", "value": "2"}, {"name": "t", "value": "1262446827"}, {"name": "type", "value": "gif"}], "headersSize": 606, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Date", "value": "Sat, 02 Jan 2010 15:40:34 GMT"}, {"name": "Server", "value": "Apache"}, {"name": "Expires", "value": "Mon, 01 Feb 2010 15:40:36 GMT"}, {"name": "Cache-Control", "value": "public, max-age=2592000"}, {"name": "Last-Modified", "value": "Sun, 15 Jan 2006 12:00:00 GMT"}, {"name": "Content-Length", "value": "1334"}, {"name": "Keep-Alive", "value": "timeout=2, max=97"}, {"name": "Connection", "value": "Keep-Alive"}, {"name": "Content-Type", "value": "image/gif"}], "content": {"size": 1334, "mimeType": "image/gif"}, "redirectURL": "", "headersSize": 300, "bodySize": 1334}, "cache": {}, "timings": {"dns": 0, "connect": 0, "blocked": 6843, "send": 0, "wait": 2212, "receive": 0}}, {"pageref": "page_12043", "startedDateTime": "2010-01-02T16:40:22.413+01:00", "time": 9067, "request": {"method": "GET", "url": "http://1.cuzillion.com/bin/resource.cgi?type=gif&sleep=2&n=8&t=1262446827", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Host", "value": "1.cuzillion.com"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows; U; Windows NT 6.0; en-US; rv:1.9.2b6pre) Gecko/20091230 Namoroka/3.6b6pre (.NET CLR 3.5.30729)"}, {"name": "Accept", "value": "image/png,image/*;q=0.8,*/*;q=0.5"}, {"name": "Accept-Language", "value": "en-us,en;q=0.5"}, {"name": "Accept-Encoding", "value": "gzip,deflate"}, {"name": "Accept-<PERSON><PERSON><PERSON>", "value": "ISO-8859-1,utf-8;q=0.7,*;q=0.7"}, {"name": "Keep-Alive", "value": "115"}, {"name": "Connection", "value": "keep-alive"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://stevesouders.com/cuzillion/?c0=bi1hfff2_0_f&c1=bi1hfff2_0_f&c2=bi1hfff2_0_f&c3=bi1hfff2_0_f&c4=bi1hfff2_0_f&c5=bi1hfff2_0_f&c6=bi1hfff2_0_f&c7=bi1hfff2_0_f&t=1258547264277"}], "queryString": [{"name": "n", "value": "8"}, {"name": "sleep", "value": "2"}, {"name": "t", "value": "1262446827"}, {"name": "type", "value": "gif"}], "headersSize": 606, "bodySize": -1}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Date", "value": "Sat, 02 Jan 2010 15:40:34 GMT"}, {"name": "Server", "value": "Apache"}, {"name": "Expires", "value": "Mon, 01 Feb 2010 15:40:36 GMT"}, {"name": "Cache-Control", "value": "public, max-age=2592000"}, {"name": "Last-Modified", "value": "Sun, 15 Jan 2006 12:00:00 GMT"}, {"name": "Keep-Alive", "value": "timeout=2, max=97"}, {"name": "Connection", "value": "Keep-Alive"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "Content-Type", "value": "image/gif"}], "content": {"size": 492, "mimeType": "image/gif"}, "redirectURL": "", "headersSize": 306, "bodySize": 492}, "cache": {}, "timings": {"dns": 0, "connect": 0, "blocked": 6850, "send": 0, "wait": 2217, "receive": 0}}]}}