/* See license.txt for terms of usage */

/**
 * @module core/lib
 */
define("core/lib", [
    "core/trace"
],

function(Trace) {

//***********************************************************************************************//

/**
 * @alias module:core/lib
 */
var Lib = {};

//***********************************************************************************************//
// Browser Version

var userAgent = navigator.userAgent.toLowerCase();
Lib.isFirefox = /firefox/.test(userAgent);
Lib.isOpera   = /opera/.test(userAgent);
Lib.isWebkit  = /webkit/.test(userAgent);
Lib.isSafari  = /webkit/.test(userAgent);
Lib.isIE      = /msie/.test(userAgent) && !/opera/.test(userAgent);
Lib.isIE6     = /msie 6/i.test(navigator.appVersion);
Lib.browserVersion = (userAgent.match( /.+(?:rv|it|ra|ie)[\/: ]([\d.]+)/ ) || [0,'0'])[1];
Lib.isIElt8   = Lib.isIE && (Lib.browserVersion-0 < 8);
Lib.supportsSelectElementText = (window.getSelection && window.document.createRange) || (window.document.body.createTextRange);

//***********************************************************************************************//
// Core concepts (extension, dispatch, bind)

Lib.extend = function copyObject(l, r)
{
    var m = {};
    Lib.append(m, l);
    Lib.append(m, r);
    return m;
};

Lib.append = function(l, r)
{
    for (var n in r)
        l[n] = r[n];
    return l;
};

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

Lib.bind = function()  // fn, thisObject, args => thisObject.fn(args, arguments);
{
    var args = Lib.cloneArray(arguments), fn = args.shift(), object = args.shift();
    return function() { return fn.apply(object, Lib.arrayInsert(Lib.cloneArray(args), 0, arguments)); }
};

Lib.bindFixed = function() // fn, thisObject, args => thisObject.fn(args);
{
    var args = Lib.cloneArray(arguments), fn = args.shift(), object = args.shift();
    return function() { return fn.apply(object, args); }
};

Lib.dispatch = function(listeners, name, args)
{
    for (var i=0; listeners && i<listeners.length; i++)
    {
        var listener = listeners[i];
        if (listener[name])
        {
            try
            {
                listener[name].apply(listener, args);
            }
            catch (exc)
            {
                Trace.exception(exc);
            }
        }
    }
};

Lib.dispatch2 = function(listeners, name, args)
{
    for (var i=0; i<listeners.length; i++)
    {
        var listener = listeners[i];
        if (listener[name])
        {
            try
            {
                var result = listener[name].apply(listener, args);
                if (result)
                    return result;
            }
            catch (exc)
            {
                Trace.exception(exc);
            }
        }
    }
};

//***********************************************************************************************//
// Type Checking

var toString = Object.prototype.toString;
var reFunction = /^\s*function(\s+[\w_$][\w\d_$]*)?\s*\(/;

Lib.isArray = function(object)
{
    //return toString.call(object) === "[object Array]";
    return jQuery.isArray(object);
};

Lib.isFunction = function(object)
{
    if (!object)
        return false;

    return toString.call(object) === "[object Function]" ||
        Lib.isIE && typeof object != "string" &&
        reFunction.test(""+object);
};

//***********************************************************************************************//
// DOM

Lib.isAncestor = function(node, potentialAncestor)
{
    for (var parent = node; parent; parent = parent.parentNode)
    {
        if (parent == potentialAncestor)
            return true;
    }

    return false;
};

//***********************************************************************************************//
// Events

Lib.fixEvent = function(e)
{
    return jQuery.event.fix(e || window.event);
}

Lib.fireEvent = function(element, event)
{
    if (document.createEvent)
    {
        var evt = document.createEvent("Events");
        evt.initEvent(event, true, false); // event type,bubbling,cancelable
        return !element.dispatchEvent(evt);
    }
}

Lib.cancelEvent = function(event)
{
    var e = Lib.fixEvent(event);
    e.stopPropagation();
    e.preventDefault();
};

Lib.addEventListener = function(object, name, handler, direction)
{
    direction = direction || false;

    if (object.addEventListener)
        object.addEventListener(name, handler, direction);
    else
        object.attachEvent("on"+name, handler);
};

Lib.removeEventListener = function(object, name, handler, direction)
{
    direction = direction || false;

    if (object.removeEventListener)
        object.removeEventListener(name, handler, direction);
    else
        object.detachEvent("on"+name, handler);
};

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
// Key Events

Lib.isLeftClick = function(event)
{
    return event.button == 0 && Lib.noKeyModifiers(event);
};

Lib.noKeyModifiers = function(event)
{
    return !event.ctrlKey && !event.shiftKey && !event.altKey && !event.metaKey;
};

Lib.isControlClick = function(event)
{
    return event.button == 0 && Lib.isControl(event);
};

Lib.isShiftClick = function(event)
{
    return event.button == 0 && Lib.isShift(event);
};

Lib.isControl = function(event)
{
    return (event.metaKey || event.ctrlKey) && !event.shiftKey && !event.altKey;
};

Lib.isAlt = function(event)
{
    return event.altKey && !event.ctrlKey && !event.shiftKey && !event.metaKey;
};

Lib.isAltClick = function(event)
{
    return event.button == 0 && Lib.isAlt(event);
};

Lib.isControlShift = function(event)
{
    return (event.metaKey || event.ctrlKey) && event.shiftKey && !event.altKey;
};

Lib.isShift = function(event)
{
    return event.shiftKey && !event.metaKey && !event.ctrlKey && !event.altKey;
};

//***********************************************************************************************//
// Rect {top, left, height, width}

Lib.inflateRect = function(rect, x, y)
{
    return {
        top: rect.top - y,
        left: rect.left - x,
        height: rect.height + 2*y,
        width: rect.width + 2*x
    }
};

Lib.pointInRect = function(rect, x, y)
{
    return (y >= rect.top && y <= rect.top + rect.height &&
        x >= rect.left && x <= rect.left + rect.width);
}

//*************************************************************************************************
// Arrays

Lib.cloneArray = function(array, fn)
{
   var newArray = [];

   if (fn)
       for (var i = 0; i < array.length; ++i)
           newArray.push(fn(array[i]));
   else
       for (var i = 0; i < array.length; ++i)
           newArray.push(array[i]);

   return newArray;
};

Lib.arrayInsert = function(array, index, other)
{
   for (var i = 0; i < other.length; ++i)
       array.splice(i+index, 0, other[i]);
   return array;
};

Lib.remove = function(list, item)
{
    for (var i = 0; i < list.length; ++i)
    {
        if (list[i] == item)
        {
            list.splice(i, 1);
            return true;
        }
    }
    return false;
};

//*************************************************************************************************
// Text Formatting

Lib.formatSize = function(bytes)
{
    var sizePrecision = 1; // Can be customizable from cookies?
    sizePrecision = (sizePrecision > 2) ? 2 : sizePrecision;
    sizePrecision = (sizePrecision < -1) ? -1 : sizePrecision;

    if (sizePrecision == -1)
        return bytes + " B";

    var a = Math.pow(10, sizePrecision);

    if (bytes == -1 || bytes == undefined)
        return "?";
    else if (bytes == 0)
        return "0";
    else if (bytes < 1024)
        return bytes + " B";
    else if (bytes < (1024*1024))
        return Math.round((bytes/1024)*a)/a + " KB";
    else
        return Math.round((bytes/(1024*1024))*a)/a + " MB";
};

Lib.formatTime = function(elapsed)
{
    if (elapsed == -1)
        return "-"; // should be &nbsp; but this will be escaped so we need something that is no whitespace
    else if (elapsed < 1000)
        return elapsed + "ms";
    else if (elapsed < 60000)
        return (Math.ceil(elapsed/10) / 100) + "s";
    else
        return (Math.ceil((elapsed/60000)*100)/100) + "m";
};

Lib.formatNumber = function(number)
{
    number += "";
    var x = number.split(".");
    var x1 = x[0];
    var x2 = x.length > 1 ? "." + x[1] : "";
    var rgx = /(\d+)(\d{3})/;
    while (rgx.test(x1))
        x1 = x1.replace(rgx, "$1" + " " + "$2");
    return x1 + x2;
};

Lib.formatString = function(string)
{
    var args = Lib.cloneArray(arguments), string = args.shift();
    for (var i=0; i<args.length; i++)
    {
        var value = args[i].toString();
        string = string.replace("%S", value);
    }
    return string;
};

//*************************************************************************************************
// Date

Lib.parseISO8601 = function(text)
{
    var date = Lib.fromISOString(text);
    return date ? date.getTime() : null;
};

Lib.fromISOString = function(text)
{
    if (!text)
        return null;

    // Date time pattern: YYYY-MM-DDThh:mm:ss.sTZD
    // eg 1997-07-16T19:20:30.451+01:00
    // http://www.w3.org/TR/NOTE-datetime
    // xxxHonza: use the one from the schema.
    var regex = /(\d\d\d\d)(-)?(\d\d)(-)?(\d\d)(T)?(\d\d)(:)?(\d\d)(:)?(\d\d)(\.\d+)?(Z|([+-])(\d\d)(:)?(\d\d))/;
    var reg = new RegExp(regex);
    var m = text.toString().match(new RegExp(regex));
    if (!m)
        return null;

    var date = new Date();
    date.setUTCDate(1);
    date.setUTCFullYear(parseInt(m[1], 10));
    date.setUTCMonth(parseInt(m[3], 10) - 1);
    date.setUTCDate(parseInt(m[5], 10));
    date.setUTCHours(parseInt(m[7], 10));
    date.setUTCMinutes(parseInt(m[9], 10));
    date.setUTCSeconds(parseInt(m[11], 10));

    if (m[12])
        date.setUTCMilliseconds(parseFloat(m[12]) * 1000);
    else
        date.setUTCMilliseconds(0);

    if (m[13] != 'Z')
    {
        var offset = (m[15] * 60) + parseInt(m[17], 10);
        offset *= ((m[14] == '-') ? -1 : 1);
        date.setTime(date.getTime() - offset * 60 * 1000);
    }

    return date;
},

Lib.toISOString = function(date)
{
    function f(n, c) {
        if (!c) c = 2;
        var s = new String(n);
        while (s.length < c) s = "0" + s;
        return s;
    }

    var result = date.getUTCFullYear() + '-' +
        f(date.getMonth() + 1) + '-' +
        f(date.getDate()) + 'T' +
        f(date.getHours()) + ':' +
        f(date.getMinutes()) + ':' +
        f(date.getSeconds()) + '.' +
        f(date.getMilliseconds(), 3);

    var offset = date.getTimezoneOffset();
    var offsetHours = Math.floor(offset / 60);
    var offsetMinutes = Math.floor(offset % 60);
    var prettyOffset = (offset > 0 ? "-" : "+") +
        f(Math.abs(offsetHours)) + ":" + f(Math.abs(offsetMinutes));

    return result + prettyOffset;
},

//*************************************************************************************************
// URL

Lib.getFileName = function(url)
{
    try
    {
        var split = Lib.splitURLBase(url);
        return split.name;
    }
    catch (e)
    {
        Trace.log(unescape(url));
    }

    return url;
};

Lib.getFileExtension = function(url)
{
    if (!url)
        return null;

    // Remove query string from the URL if any.
    var queryString = url.indexOf("?");
    if (queryString != -1)
        url = url.substr(0, queryString);

    // Now get the file extension.
    var lastDot = url.lastIndexOf(".");
    return url.substr(lastDot+1);
};

Lib.splitURLBase = function(url)
{
    if (Lib.isDataURL(url))
        return Lib.splitDataURL(url);
    return Lib.splitURLTrue(url);
};

Lib.isDataURL = function(url)
{
    return (url && url.substr(0,5) == "data:");
};

Lib.splitDataURL = function(url)
{
    var mark = url.indexOf(':', 3);
    if (mark != 4)
        return false;   //  the first 5 chars must be 'data:'

    var point = url.indexOf(',', mark+1);
    if (point < mark)
        return false; // syntax error

    var props = { encodedContent: url.substr(point+1) };

    var metadataBuffer = url.substr(mark+1, point);
    var metadata = metadataBuffer.split(';');
    for (var i = 0; i < metadata.length; i++)
    {
        var nv = metadata[i].split('=');
        if (nv.length == 2)
            props[nv[0]] = nv[1];
    }

    // Additional Firebug-specific properties
    if (props.hasOwnProperty('fileName'))
    {
         var caller_URL = decodeURIComponent(props['fileName']);
         var caller_split = Lib.splitURLTrue(caller_URL);

        if (props.hasOwnProperty('baseLineNumber'))  // this means it's probably an eval()
        {
            props['path'] = caller_split.path;
            props['line'] = props['baseLineNumber'];
            var hint = decodeURIComponent(props['encodedContent'].substr(0,200)).replace(/\s*$/, "");
            props['name'] =  'eval->'+hint;
        }
        else
        {
            props['name'] = caller_split.name;
            props['path'] = caller_split.path;
        }
    }
    else
    {
        if (!props.hasOwnProperty('path'))
            props['path'] = "data:";
        if (!props.hasOwnProperty('name'))
            props['name'] =  decodeURIComponent(props['encodedContent'].substr(0,200)).replace(/\s*$/, "");
    }

    return props;
};

Lib.splitURLTrue = function(url)
{
    var reSplitFile = /:\/{1,3}(.*?)\/([^\/]*?)\/?($|\?.*)/;
    var m = reSplitFile.exec(url);
    if (!m)
        return {name: url, path: url};
    else if (!m[2])
        return {path: m[1], name: m[1]};
    else
        return {path: m[1], name: m[2]+m[3]};
};

/**
 * Returns value of specified parameter in the current URL.
 * @param {String} name Name of the requested parameter.
 * @return {String} Value of the requested parameter.
 */
Lib.getURLParameter = function(name)
{
    var query = window.location.search.substring(1);
    var vars = query.split("&");
    for (var i=0;i<vars.length;i++)
    {
        var pair = vars[i].split("=");
        if (pair[0] == name)
            return unescape(pair[1]);
    }
    return null;
};

/**
 * Supports multiple URL parameters with the same name. Returns array
 * of values.
 * @param {String} name Name of the requested parameter.
 * @return {Array} Array with values.
 */
Lib.getURLParameters = function(name)
{
    var result = [];
    var query = window.location.search.substring(1);
    var vars = query.split("&");
    for (var i=0;i<vars.length;i++)
    {
        var pair = vars[i].split("=");
        if (pair[0] == name)
            result.push(unescape(pair[1]));
    }
    return result;
};

/**
 * Supports multiple hash parameters with the same name. Returns array
 * of values.
 * @param {String} name Name of the requested hash parameter.
 * @return {Array} Array with values.
 */
Lib.getHashParameters = function(name)
{
    var result = [];
    var query = window.location.hash.substring(1);
    var vars = query.split("&");
    for (var i=0;i<vars.length;i++)
    {
        var pair = vars[i].split("=");
        if (pair[0] == name)
            result.push(unescape(pair[1]));
    }
    return result;
};

Lib.parseURLParams = function(url)
{
    var q = url ? url.indexOf("?") : -1;
    if (q == -1)
        return [];

    var search = url.substr(q+1);
    var h = search.lastIndexOf("#");
    if (h != -1)
        search = search.substr(0, h);

    if (!search)
        return [];

    return Lib.parseURLEncodedText(search);
};

Lib.parseURLEncodedText = function(text, noLimit)
{
    var maxValueLength = 25000;

    var params = [];

    // In case the text is empty just return the empty parameters
    if(text == '')
      return params;

    // Unescape '+' characters that are used to encode a space.
    // See section 2.2.in RFC 3986: http://www.ietf.org/rfc/rfc3986.txt
    text = text.replace(/\+/g, " ");

    // Unescape '&amp;' character
    //xxxHonza: text = Lib.unescapeForURL(text);

    function decodeText(text)
    {
        try
        {
            return decodeURIComponent(text);
        }
        catch (e)
        {
            return decodeURIComponent(unescape(text));
        }
    }

    var args = text.split("&");
    for (var i = 0; i < args.length; ++i)
    {
        try
        {
            var index = args[i].indexOf("=");
            if (index != -1)
            {
                var paramName = args[i].substring(0, index);
                var paramValue = args[i].substring(index + 1);

                if (paramValue.length > maxValueLength && !noLimit)
                    paramValue = Lib.$STR("LargeData");

                params.push({name: decodeText(paramName), value: decodeText(paramValue)});
            }
            else
            {
                var paramName = args[i];
                params.push({name: decodeText(paramName), value: ""});
            }
        }
        catch (e)
        {
        }
    }

    params.sort(function(a, b) { return a.name <= b.name ? -1 : 1; });

    return params;
};

//*************************************************************************************************
// DOM

Lib.getBody = function(doc)
{
    if (doc.body)
        return doc.body;

    var body = doc.getElementsByTagName("body")[0];
    if (body)
        return body;

    // Should never happen.
    return null;
};

Lib.getHead = function(doc)
{
    return doc.getElementsByTagName("head")[0];
};

Lib.getAncestorByClass = function(node, className)
{
    for (var parent = node; parent; parent = parent.parentNode)
    {
        if (Lib.hasClass(parent, className))
            return parent;
    }

    return null;
};

Lib.$ = function()
{
    return Lib.getElementByClass.apply(this, arguments);
};

Lib.getElementByClass = function(node, className)  // className, className, ...
{
    if (!node)
        return null;

    var args = Lib.cloneArray(arguments); args.splice(0, 1);
    for (var child = node.firstChild; child; child = child.nextSibling)
    {
        var args1 = Lib.cloneArray(args); args1.unshift(child);
        if (Lib.hasClass.apply(this, args1))
            return child;
        else
        {
            var found = Lib.getElementByClass.apply(this, args1);
            if (found)
                return found;
        }
    }

    return null;
};

Lib.getElementsByClass = function(node, className)  // className, className, ...
{
    if (node.querySelectorAll)
    {
        var args = Lib.cloneArray(arguments); args.shift();
        var selector = "." + args.join(".");
        return node.querySelectorAll(selector);
    }

    function iteratorHelper(node, classNames, result)
    {
        for (var child = node.firstChild; child; child = child.nextSibling)
        {
            var args1 = Lib.cloneArray(classNames); args1.unshift(child);
            if (Lib.hasClass.apply(null, args1))
                result.push(child);

            iteratorHelper(child, classNames, result);
        }
    }

    var result = [];
    var args = Lib.cloneArray(arguments); args.shift();
    iteratorHelper(node, args, result);
    return result;
}

Lib.getChildByClass = function(node) // ,classname, classname, classname...
{
    for (var i = 1; i < arguments.length; ++i)
    {
        var className = arguments[i];
        var child = node.firstChild;
        node = null;
        for (; child; child = child.nextSibling)
        {
            if (Lib.hasClass(child, className))
            {
                node = child;
                break;
            }
        }
    }

    return node;
};

Lib.eraseNode = function(node)
{
    while (node.lastChild)
        node.removeChild(node.lastChild);
};

Lib.clearNode = function(node)
{
    node.innerHTML = "";
};

//***********************************************************************************************//
// CSS

Lib.hasClass = function(node, name) // className, className, ...
{
    if (!node || node.nodeType != 1)
        return false;
    else
    {
        for (var i=1; i<arguments.length; ++i)
        {
            var name = arguments[i];
            //var re = new RegExp("(^|\\s)"+name+"($|\\s)");
            //if (!re.exec(node.getAttribute("class")))
            //    return false;
            var className = node.className;//node.getAttribute("class");
            if (!className || className.indexOf(name + " ") == -1)
                return false;
        }

        return true;
    }
};

Lib.setClass = function(node, name)
{
    if (node && !Lib.hasClass(node, name))
        node.className += " " + name + " ";
};

Lib.removeClass = function(node, name)
{
    if (node && node.className)
    {
        var index = node.className.indexOf(name);
        if (index >= 0)
        {
            var size = name.length;
            node.className = node.className.substr(0,index-1) + node.className.substr(index+size);
        }
    }
};

Lib.toggleClass = function(elt, name)
{
    if (Lib.hasClass(elt, name))
    {
        Lib.removeClass(elt, name);
        return false;
    }
    else
    {
        Lib.setClass(elt, name);
        return true;
    }
};

Lib.setClassTimed = function(elt, name, timeout)
{
    if (!timeout)
        timeout = 1300;

    if (elt.__setClassTimeout)  // then we are already waiting to remove the class mark
        clearTimeout(elt.__setClassTimeout);  // reset the timer
    else                        // then we are not waiting to remove the mark
        Lib.setClass(elt, name);

    elt.__setClassTimeout = setTimeout(function()
    {
        delete elt.__setClassTimeout;
        Lib.removeClass(elt, name);
    }, timeout);
};

//*************************************************************************************************
// Text

Lib.startsWith = function(str, searchString, position)
{
    position = position || 0;
    return str.indexOf(searchString, position) === position;
};

Lib.trim = function(text)
{
    return text.replace(/^\s*|\s*$/g, "");
};

Lib.wrapText = function(text, noEscapeHTML)
{
    var reNonAlphaNumeric = /[^A-Za-z_$0-9'"-]/;

    var html = [];
    var wrapWidth = 100;

    // Split long text into lines and put every line into an <pre> element (only in case
    // if noEscapeHTML is false). This is useful for automatic scrolling when searching
    // within response body (in order to scroll we need an element).
    var lines = Lib.splitLines(text);
    for (var i = 0; i < lines.length; ++i)
    {
        var line = lines[i];
        while (line.length > wrapWidth)
        {
            var m = reNonAlphaNumeric.exec(line.substr(wrapWidth, 100));
            var wrapIndex = wrapWidth+ (m ? m.index : 0);
            var subLine = line.substr(0, wrapIndex);
            line = line.substr(wrapIndex);

            if (!noEscapeHTML) html.push("<pre>");
            html.push(noEscapeHTML ? subLine : Lib.escapeHTML(subLine));
            if (!noEscapeHTML) html.push("</pre>");
        }

        if (!noEscapeHTML) html.push("<pre>");
        html.push(noEscapeHTML ? line : Lib.escapeHTML(line));
        if (!noEscapeHTML) html.push("</pre>");
    }

    return html.join(noEscapeHTML ? "\n" : "");
};

Lib.insertWrappedText = function(text, textBox, noEscapeHTML)
{
    textBox.innerHTML = "<pre>" + Lib.wrapText(text, noEscapeHTML) + "</pre>";
};

Lib.splitLines = function(text)
{
    var reSplitLines = /\r\n|\r|\n/;

    if (!text)
        return [];
    else if (text.split)
        return text.split(reSplitLines);

    var str = text + "";
    var theSplit = str.split(reSplitLines);
    return theSplit;
};

Lib.getPrettyDomain = function(url)
{
    // Large data URIs cause performance problems.
    // 255 is the FQDN length limit per RFC 1035.
    var m = /^(?!data:)[^:]+:\/{1,3}(www\.)?([^\/]{1,256})/.exec(url);
    return m ? m[2] : "";
},

Lib.escapeHTML = function(value)
{
    function replaceChars(ch)
    {
        switch (ch)
        {
            case "<":
                return "&lt;";
            case ">":
                return "&gt;";
            case "&":
                return "&amp;";
            case "'":
                return "&#39;";
            case '"':
                return "&quot;";
        }
        return "?";
    };
    return String(value).replace(/[<>&"']/g, replaceChars);
};

Lib.cropString = function(text, limit)
{
    text = text + "";

    if (!limit)
        var halfLimit = 50;
    else
        var halfLimit = limit / 2;

    if (text.length > limit)
        return Lib.escapeNewLines(text.substr(0, halfLimit) + "..." + text.substr(text.length-halfLimit));
    else
        return Lib.escapeNewLines(text);
};

Lib.escapeNewLines = function(value)
{
    return value.replace(/\r/g, "\\r").replace(/\n/g, "\\n");
};

//***********************************************************************************************//
// JSON

Lib.cloneJSON = function(obj)
{
    if (obj == null || typeof(obj) != "object")
        return obj;

    try
    {
        var temp = obj.constructor();
        for (var key in obj)
            temp[key] = this.cloneJSON(obj[key]);
        return temp;
    }
    catch (err)
    {
        Trace.exception(err);
    }

    return null;
};

//***********************************************************************************************//
// Layout

Lib.getOverflowParent = function(element)
{
    for (var scrollParent = element.parentNode; scrollParent;
        scrollParent = scrollParent.offsetParent)
    {
        if (scrollParent.scrollHeight > scrollParent.offsetHeight)
            return scrollParent;
    }
};

Lib.getElementBox = function(el)
{
    var result = {};

    if (el.getBoundingClientRect)
    {
        var rect = el.getBoundingClientRect();

        // fix IE problem with offset when not in fullscreen mode
        var offset = Lib.isIE ? document.body.clientTop || document.documentElement.clientTop: 0;
        var scroll = Lib.getWindowScrollPosition();

        result.top = Math.round(rect.top - offset + scroll.top);
        result.left = Math.round(rect.left - offset + scroll.left);
        result.height = Math.round(rect.bottom - rect.top);
        result.width = Math.round(rect.right - rect.left);
    }
    else
    {
        var position = Lib.getElementPosition(el);

        result.top = position.top;
        result.left = position.left;
        result.height = el.offsetHeight;
        result.width = el.offsetWidth;
    }

    return result;
};

Lib.getElementPosition = function(el)
{
    var left = 0
    var top = 0;

    do
    {
        left += el.offsetLeft;
        top += el.offsetTop;
    }
    while (el = el.offsetParent);

    return {left:left, top:top};
};

Lib.getWindowSize = function()
{
    var width=0, height=0, el;

    if (typeof window.innerWidth == "number")
    {
        width = window.innerWidth;
        height = window.innerHeight;
    }
    else if ((el=document.documentElement) && (el.clientHeight || el.clientWidth))
    {
        width = el.clientWidth;
        height = el.clientHeight;
    }
    else if ((el=document.body) && (el.clientHeight || el.clientWidth))
    {
        width = el.clientWidth;
        height = el.clientHeight;
    }

    return {width: width, height: height};
};

Lib.getWindowScrollSize = function()
{
    var width=0, height=0, el;

    // first try the document.documentElement scroll size
    if (!Lib.isIEQuiksMode && (el=document.documentElement) &&
       (el.scrollHeight || el.scrollWidth))
    {
        width = el.scrollWidth;
        height = el.scrollHeight;
    }

    // then we need to check if document.body has a bigger scroll size value
    // because sometimes depending on the browser and the page, the document.body
    // scroll size returns a smaller (and wrong) measure
    if ((el=document.body) && (el.scrollHeight || el.scrollWidth) &&
        (el.scrollWidth > width || el.scrollHeight > height))
    {
        width = el.scrollWidth;
        height = el.scrollHeight;
    }

    return {width: width, height: height};
};

Lib.getWindowScrollPosition = function()
{
    var top=0, left=0, el;

    if(typeof window.pageYOffset == "number")
    {
        top = window.pageYOffset;
        left = window.pageXOffset;
    }
    else if((el=document.body) && (el.scrollTop || el.scrollLeft))
    {
        top = el.scrollTop;
        left = el.scrollLeft;
    }
    else if((el=document.documentElement) && (el.scrollTop || el.scrollLeft))
    {
        top = el.scrollTop;
        left = el.scrollLeft;
    }

    return {top:top, left:left};
};

// ********************************************************************************************* //
// Scrolling

Lib.scrollIntoCenterView = function(element, scrollBox, notX, notY)
{
    if (!element)
        return;

    if (!scrollBox)
        scrollBox = Lib.getOverflowParent(element);

    if (!scrollBox)
        return;

    var offset = Lib.getClientOffset(element);

    if (!notY)
    {
        var topSpace = offset.y - scrollBox.scrollTop;
        var bottomSpace = (scrollBox.scrollTop + scrollBox.clientHeight)
            - (offset.y + element.offsetHeight);

        if (topSpace < 0 || bottomSpace < 0)
        {
            var centerY = offset.y - (scrollBox.clientHeight/2);
            scrollBox.scrollTop = centerY;
        }
    }

    if (!notX)
    {
        var leftSpace = offset.x - scrollBox.scrollLeft;
        var rightSpace = (scrollBox.scrollLeft + scrollBox.clientWidth)
            - (offset.x + element.clientWidth);

        if (leftSpace < 0 || rightSpace < 0)
        {
            var centerX = offset.x - (scrollBox.clientWidth/2);
            scrollBox.scrollLeft = centerX;
        }
    }
};

Lib.getClientOffset = function(elt)
{
    function addOffset(elt, coords, view)
    {
        var p = elt.offsetParent;

        var style = view.getComputedStyle(elt, "");

        if (elt.offsetLeft)
            coords.x += elt.offsetLeft + parseInt(style.borderLeftWidth);
        if (elt.offsetTop)
            coords.y += elt.offsetTop + parseInt(style.borderTopWidth);

        if (p)
        {
            if (p.nodeType == 1)
                addOffset(p, coords, view);
        }
        else if (elt.ownerDocument.defaultView.frameElement)
        {
            addOffset(elt.ownerDocument.defaultView.frameElement,
                coords, elt.ownerDocument.defaultView);
        }
    }

    var coords = {x: 0, y: 0};
    if (elt)
    {
        var view = elt.ownerDocument.defaultView;
        addOffset(elt, coords, view);
    }

    return coords;
};

// ********************************************************************************************* //
// Stylesheets

/**
 * Load stylesheet into the specified document. The method doesn't wait till the stylesheet
 * is loaded and so, not suitable for cases when you do not care when the file is loaded.
 * @param {Object} doc The document to load the stylesheet into.
 * @param {Object} url URL of the target stylesheet.
 */
Lib.addStyleSheet = function(doc, url)
{
    if (doc.getElementById(url))
        return;

    var link = doc.createElement("link");
    link.type = "text/css";
    link.rel = "stylesheet";
    link.href = url;
    link.setAttribute("id", url);

    var head = Lib.getHead(doc);
    head.appendChild(link);
}

// ********************************************************************************************* //
// Selection

Lib.selectElementText = function(textNode, startOffset, endOffset)
{
    var win = window;
    var doc = win.document;
    if (win.getSelection && doc.createRange)
    {
        var sel = win.getSelection();
        var range = doc.createRange();
        //range.selectNodeContents(el);

        range.setStart(textNode, startOffset);
        range.setEnd(textNode, endOffset);
        sel.removeAllRanges();
        sel.addRange(range);
    }
    else if (doc.body.createTextRange)
    {
        range = doc.body.createTextRange();
        range.moveToElementText(textNode);
        range.select();
    }
}

// ********************************************************************************************* //

return Lib;

// ********************************************************************************************* //
});

/* See license.txt for terms of usage */

/**
 * @module domplate/tabView
 */
define("domplate/tabView", [
    "domplate/domplate",
    "core/lib",
    "core/trace"
],

function(Domplate, Lib, Trace) { with (Domplate) {

//*************************************************************************************************

/**
 * @domplate TabViewTempl is a template used by {@link TabView} widget.
 */
var TabViewTempl = domplate(
/** @lends TabViewTempl */
{
    tag:
        TABLE({"class": "tabView", cellpadding: 0, cellspacing: 0,
            _repObject: "$tabView"},
            TBODY(
                TR({"class": "tabViewRow"},
                    TD({"class": "tabViewCol", valign: "top"},
                        TAG("$tabList", {tabView: "$tabView"})
                    )
                )
            )
        ),

    tabList:
        DIV({"class": "tabViewBody", onclick: "$onClickTab"},
            DIV({"class": "$tabView.id\\Bar tabBar"}),
            DIV({"class": "$tabView.id\\Bodies tabBodies"})
        ),

    tabHeaderTag:
        A({"class": "$tab.id\\Tab tab",
            view: "$tab.id", _repObject: "$tab"},
            "$tab.label"
        ),

    tabBodyTag:
        DIV({"class": "tab$tab.id\\Body tabBody", _repObject: "$tab"}),

    // * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
    // Event Handlers

    hideTab: function(context)
    {
        return false;
    },

    onClickTab: function(event)
    {
        var e = Lib.fixEvent(event);
        var tabView = this.getTabView(e.target);
        tabView.onClickTab(e);
    },

    // * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
    // Coupling with TabView instance.

    getTabView: function(node)
    {
        var tabView = Lib.getAncestorByClass(node, "tabView");
        return tabView.repObject;
    }
});

//*************************************************************************************************

function TabView(id)
{
    this.id = id;
    this.tabs = [];
    this.listeners = [];
    this.tabBarVisibility = true;
}

/**
 * @widget TabView represents a widget for tabbed UI interface.
 */
TabView.prototype =
/** @lends TabView */
{
    appendTab: function(tab)
    {
        this.tabs.push(tab);
        tab.tabView = this;
        return tab;
    },

    removeTab: function(tabId)
    {
        for (var i in this.tabs)
        {
            var tab = this.tabs[i];
            if (tab.id == tabId)
            {
                this.tabs.splice(i, 1);
                break;
            }
        }
    },

    getTab: function(tabId)
    {
        for (var i in this.tabs)
        {
            var tab = this.tabs[i];
            if (tab.id == tabId)
                return tab;
        }
    },

    selectTabByName: function(tabId)
    {
        var tab = Lib.getElementByClass(this.element, tabId + "Tab");
        if (tab)
            this.selectTab(tab);
    },

    showTabBar: function(show)
    {
        if (this.element)
        {
            if (show)
                this.element.removeAttribute("hideTabBar");
            else
                this.element.setAttribute("hideTabBar", "true");
        }
        else
        {
            this.tabBarVisibility = show;
        }
    },

    // * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
    // Listeners

    addListener: function(listener)
    {
        this.listeners.push(listener);
    },

    removeListener: function(listener)
    {
        Lib.remove(this.listeners, listener);
    },

    // * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

    // xxxHonza: this should be private.
    onClickTab: function(e)
    {
        var tab = Lib.getAncestorByClass(e.target, "tab");
        if (tab)
            this.selectTab(tab);
    },

    selectTab: function(tab)
    {
        if (!Lib.hasClass(tab, "tab"))
            return;

        if (Lib.hasClass(tab, "selected") && tab._updated)
            return;

        var view = tab.getAttribute("view");

        // xxxHonza: this is null if the user clicks on an example on the home page.
        if (!view)
            return;

        var viewBody = Lib.getAncestorByClass(tab, "tabViewBody");

        // Deactivate current tab.
        if (viewBody.selectedTab)
        {
            viewBody.selectedTab.removeAttribute("selected");
            viewBody.selectedBody.removeAttribute("selected");

            // IE workaround. Removing the "selected" attribute
            // doesn't update the style (associated using attribute selector).
            // So use a class name instead.
            Lib.removeClass(viewBody.selectedTab, "selected");
            Lib.removeClass(viewBody.selectedBody, "selected");
        }

        // Store info about new active tab. Each tab has to have a body, 
        // which is identified by class.
        var tabBody = Lib.getElementByClass(viewBody, "tab" + view + "Body");
        if (!tabBody)
            Trace.error("TabView.selectTab; Missing tab body", tab);

        viewBody.selectedTab = tab;
        viewBody.selectedBody = tabBody;

        // Activate new tab.
        viewBody.selectedTab.setAttribute("selected", "true");
        viewBody.selectedBody.setAttribute("selected", "true");

        // IE workaround. Adding the "selected" attribute doesn't
        // update the style. Use class name instead.
        Lib.setClass(viewBody.selectedBody, "selected");
        Lib.setClass(viewBody.selectedTab, "selected");

        this.updateTabBody(viewBody, view);
    },

    // xxxHonza: should be private
    updateTabBody: function(viewBody, view)
    {
        var tab = viewBody.selectedTab.repObject;
        if (tab._body._updated)
            return;

        tab._body._updated = true;

        // Render default content if available.
        if (tab.bodyTag)
            tab.bodyTag.replace({tab: tab}, tab._body);

        // Call also onUpdateBody for dynamic body update.
        if (tab && tab.onUpdateBody)
            tab.onUpdateBody(this, tab._body);

        // Dispatch to all listeners.
        for (var i=0; i<this.listeners.length; i++)
        {
            var listener = this.listeners[i];
            if (listener.onUpdateBody)
                listener.onUpdateBody(this, tab._body);
        }
    },

    // * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

    render: function(parentNode)
    {
        this.element = TabViewTempl.tag.replace({tabView: this}, parentNode, TabViewTempl);
        Lib.setClass(this.element, this.id);

        this.showTabBar(this.tabBarVisibility);

        for (var i in this.tabs)
        {
            var tab = this.tabs[i];
            var tabHeaderTag = tab.tabHeaderTag ? tab.tabHeaderTag : TabViewTempl.tabHeaderTag;
            var tabBodyTag = tab.tabBodyTag ? tab.tabBodyTag : TabViewTempl.tabBodyTag;

            try
            {
                tab._header = tabHeaderTag.append({tab:tab}, Lib.$(parentNode, "tabBar"));
                tab._body = tabBodyTag.append({tab:tab}, Lib.$(parentNode, "tabBodies"));
            }
            catch (e)
            {
                Trace.exception("TabView.appendTab; Exception ", e);
            }
        }

        return this.element;
    }
}

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

TabView.Tab = function() {}
TabView.Tab.prototype =
{
    invalidate: function()
    {
        this._updated = false;
    },

    select: function()
    {
        this.tabView.selectTabByName(this.id);
    }
}

return TabView;

// ************************************************************************************************
}});

/**
 * @license RequireJS i18n 2.0.5 Copyright (c) 2010-2012, The Dojo Foundation All Rights Reserved.
 * Available via the MIT or new BSD license.
 * see: http://github.com/requirejs/i18n for details
 */
/*jslint regexp: true */
/*global require: false, navigator: false, define: false */

/**
 * This plugin handles i18n! prefixed modules. It does the following:
 *
 * 1) A regular module can have a dependency on an i18n bundle, but the regular
 * module does not want to specify what locale to load. So it just specifies
 * the top-level bundle, like "i18n!nls/colors".
 *
 * This plugin will load the i18n bundle at nls/colors, see that it is a root/master
 * bundle since it does not have a locale in its name. It will then try to find
 * the best match locale available in that master bundle, then request all the
 * locale pieces for that best match locale. For instance, if the locale is "en-us",
 * then the plugin will ask for the "en-us", "en" and "root" bundles to be loaded
 * (but only if they are specified on the master bundle).
 *
 * Once all the bundles for the locale pieces load, then it mixes in all those
 * locale pieces into each other, then finally sets the context.defined value
 * for the nls/colors bundle to be that mixed in locale.
 *
 * 2) A regular module specifies a specific locale to load. For instance,
 * i18n!nls/fr-fr/colors. In this case, the plugin needs to load the master bundle
 * first, at nls/colors, then figure out what the best match locale is for fr-fr,
 * since maybe only fr or just root is defined for that locale. Once that best
 * fit is found, all of its locale pieces need to have their bundles loaded.
 *
 * Once all the bundles for the locale pieces load, then it mixes in all those
 * locale pieces into each other, then finally sets the context.defined value
 * for the nls/fr-fr/colors bundle to be that mixed in locale.
 */
(function () {
    'use strict';

    //regexp for reconstructing the master bundle name from parts of the regexp match
    //nlsRegExp.exec("foo/bar/baz/nls/en-ca/foo") gives:
    //["foo/bar/baz/nls/en-ca/foo", "foo/bar/baz/nls/", "/", "/", "en-ca", "foo"]
    //nlsRegExp.exec("foo/bar/baz/nls/foo") gives:
    //["foo/bar/baz/nls/foo", "foo/bar/baz/nls/", "/", "/", "foo", ""]
    //so, if match[5] is blank, it means this is the top bundle definition.
    var nlsRegExp = /(^.*(^|\/)nls(\/|$))([^\/]*)\/?([^\/]*)/;

    //Helper function to avoid repeating code. Lots of arguments in the
    //desire to stay functional and support RequireJS contexts without having
    //to know about the RequireJS contexts.
    function addPart(locale, master, needed, toLoad, prefix, suffix) {
        if (master[locale]) {
            needed.push(locale);
            if (master[locale] === true || master[locale] === 1) {
                toLoad.push(prefix + locale + '/' + suffix);
            }
        }
    }

    function addIfExists(req, locale, toLoad, prefix, suffix) {
        var fullName = prefix + locale + '/' + suffix;
        if (require._fileExists(req.toUrl(fullName + '.js'))) {
            toLoad.push(fullName);
        }
    }

    /**
     * Simple function to mix in properties from source into target,
     * but only if target does not already have a property of the same name.
     * This is not robust in IE for transferring methods that match
     * Object.prototype names, but the uses of mixin here seem unlikely to
     * trigger a problem related to that.
     */
    function mixin(target, source, force) {
        var prop;
        for (prop in source) {
            if (source.hasOwnProperty(prop) && (!target.hasOwnProperty(prop) || force)) {
                target[prop] = source[prop];
            } else if (typeof source[prop] === 'object') {
                if (!target[prop] && source[prop]) {
                    target[prop] = {};
                }
                mixin(target[prop], source[prop], force);
            }
        }
    }

    define('i18n',['module'], function (module) {
        var masterConfig = module.config ? module.config() : {};

        return {
            version: '2.0.5',
            /**
             * Called when a dependency needs to be loaded.
             */
            load: function (name, req, onLoad, config) {
                config = config || {};

                if (config.locale) {
                    masterConfig.locale = config.locale;
                }

                var masterName,
                    match = nlsRegExp.exec(name),
                    prefix = match[1],
                    locale = match[4],
                    suffix = match[5],
                    parts = locale.split("-"),
                    toLoad = [],
                    value = {},
                    i, part, current = "";

                //If match[5] is blank, it means this is the top bundle definition,
                //so it does not have to be handled. Locale-specific requests
                //will have a match[4] value but no match[5]
                if (match[5]) {
                    //locale-specific bundle
                    prefix = match[1];
                    masterName = prefix + suffix;
                } else {
                    //Top-level bundle.
                    masterName = name;
                    suffix = match[4];
                    locale = masterConfig.locale;
                    if (!locale) {
                        locale = masterConfig.locale =
                            typeof navigator === "undefined" ? "root" :
                            ((navigator.languages && navigator.languages[0]) ||
                             navigator.language ||
                             navigator.userLanguage || "root").toLowerCase();
                    }
                    parts = locale.split("-");
                }

                if (config.isBuild) {
                    //Check for existence of all locale possible files and
                    //require them if exist.
                    toLoad.push(masterName);
                    addIfExists(req, "root", toLoad, prefix, suffix);
                    for (i = 0; i < parts.length; i++) {
                        part = parts[i];
                        current += (current ? "-" : "") + part;
                        addIfExists(req, current, toLoad, prefix, suffix);
                    }

                    req(toLoad, function () {
                        onLoad();
                    });
                } else {
                    //First, fetch the master bundle, it knows what locales are available.
                    req([masterName], function (master) {
                        //Figure out the best fit
                        var needed = [],
                            part;

                        //Always allow for root, then do the rest of the locale parts.
                        addPart("root", master, needed, toLoad, prefix, suffix);
                        for (i = 0; i < parts.length; i++) {
                            part = parts[i];
                            current += (current ? "-" : "") + part;
                            addPart(current, master, needed, toLoad, prefix, suffix);
                        }

                        //Load all the parts missing.
                        req(toLoad, function () {
                            var i, partBundle, part;
                            for (i = needed.length - 1; i > -1 && needed[i]; i--) {
                                part = needed[i];
                                partBundle = master[part];
                                if (partBundle === true || partBundle === 1) {
                                    partBundle = req(prefix + part + '/' + suffix);
                                }
                                mixin(value, partBundle);
                            }

                            //All done, notify the loader.
                            onLoad(value);
                        });
                    });
                }
            }
        };
    });
}());

/* See license.txt for terms of usage */

define(
'nls/homeTab',{
    "root": {
        "homeTabLabel": "Load",
        "loadingHar": "Loading..."
    }
});


/**
 * @license RequireJS text 2.0.14 Copyright (c) 2010-2014, The Dojo Foundation All Rights Reserved.
 * Available via the MIT or new BSD license.
 * see: http://github.com/requirejs/text for details
 */
/*jslint regexp: true */
/*global require, XMLHttpRequest, ActiveXObject,
  define, window, process, Packages,
  java, location, Components, FileUtils */

define('text',['module'], function (module) {
    'use strict';

    var text, fs, Cc, Ci, xpcIsWindows,
        progIds = ['Msxml2.XMLHTTP', 'Microsoft.XMLHTTP', 'Msxml2.XMLHTTP.4.0'],
        xmlRegExp = /^\s*<\?xml(\s)+version=[\'\"](\d)*.(\d)*[\'\"](\s)*\?>/im,
        bodyRegExp = /<body[^>]*>\s*([\s\S]+)\s*<\/body>/im,
        hasLocation = typeof location !== 'undefined' && location.href,
        defaultProtocol = hasLocation && location.protocol && location.protocol.replace(/\:/, ''),
        defaultHostName = hasLocation && location.hostname,
        defaultPort = hasLocation && (location.port || undefined),
        buildMap = {},
        masterConfig = (module.config && module.config()) || {};

    text = {
        version: '2.0.14',

        strip: function (content) {
            //Strips <?xml ...?> declarations so that external SVG and XML
            //documents can be added to a document without worry. Also, if the string
            //is an HTML document, only the part inside the body tag is returned.
            if (content) {
                content = content.replace(xmlRegExp, "");
                var matches = content.match(bodyRegExp);
                if (matches) {
                    content = matches[1];
                }
            } else {
                content = "";
            }
            return content;
        },

        jsEscape: function (content) {
            return content.replace(/(['\\])/g, '\\$1')
                .replace(/[\f]/g, "\\f")
                .replace(/[\b]/g, "\\b")
                .replace(/[\n]/g, "\\n")
                .replace(/[\t]/g, "\\t")
                .replace(/[\r]/g, "\\r")
                .replace(/[\u2028]/g, "\\u2028")
                .replace(/[\u2029]/g, "\\u2029");
        },

        createXhr: masterConfig.createXhr || function () {
            //Would love to dump the ActiveX crap in here. Need IE 6 to die first.
            var xhr, i, progId;
            if (typeof XMLHttpRequest !== "undefined") {
                return new XMLHttpRequest();
            } else if (typeof ActiveXObject !== "undefined") {
                for (i = 0; i < 3; i += 1) {
                    progId = progIds[i];
                    try {
                        xhr = new ActiveXObject(progId);
                    } catch (e) {}

                    if (xhr) {
                        progIds = [progId];  // so faster next time
                        break;
                    }
                }
            }

            return xhr;
        },

        /**
         * Parses a resource name into its component parts. Resource names
         * look like: module/name.ext!strip, where the !strip part is
         * optional.
         * @param {String} name the resource name
         * @returns {Object} with properties "moduleName", "ext" and "strip"
         * where strip is a boolean.
         */
        parseName: function (name) {
            var modName, ext, temp,
                strip = false,
                index = name.lastIndexOf("."),
                isRelative = name.indexOf('./') === 0 ||
                             name.indexOf('../') === 0;

            if (index !== -1 && (!isRelative || index > 1)) {
                modName = name.substring(0, index);
                ext = name.substring(index + 1);
            } else {
                modName = name;
            }

            temp = ext || modName;
            index = temp.indexOf("!");
            if (index !== -1) {
                //Pull off the strip arg.
                strip = temp.substring(index + 1) === "strip";
                temp = temp.substring(0, index);
                if (ext) {
                    ext = temp;
                } else {
                    modName = temp;
                }
            }

            return {
                moduleName: modName,
                ext: ext,
                strip: strip
            };
        },

        xdRegExp: /^((\w+)\:)?\/\/([^\/\\]+)/,

        /**
         * Is an URL on another domain. Only works for browser use, returns
         * false in non-browser environments. Only used to know if an
         * optimized .js version of a text resource should be loaded
         * instead.
         * @param {String} url
         * @returns Boolean
         */
        useXhr: function (url, protocol, hostname, port) {
            var uProtocol, uHostName, uPort,
                match = text.xdRegExp.exec(url);
            if (!match) {
                return true;
            }
            uProtocol = match[2];
            uHostName = match[3];

            uHostName = uHostName.split(':');
            uPort = uHostName[1];
            uHostName = uHostName[0];

            return (!uProtocol || uProtocol === protocol) &&
                   (!uHostName || uHostName.toLowerCase() === hostname.toLowerCase()) &&
                   ((!uPort && !uHostName) || uPort === port);
        },

        finishLoad: function (name, strip, content, onLoad) {
            content = strip ? text.strip(content) : content;
            if (masterConfig.isBuild) {
                buildMap[name] = content;
            }
            onLoad(content);
        },

        load: function (name, req, onLoad, config) {
            //Name has format: some.module.filext!strip
            //The strip part is optional.
            //if strip is present, then that means only get the string contents
            //inside a body tag in an HTML string. For XML/SVG content it means
            //removing the <?xml ...?> declarations so the content can be inserted
            //into the current doc without problems.

            // Do not bother with the work if a build and text will
            // not be inlined.
            if (config && config.isBuild && !config.inlineText) {
                onLoad();
                return;
            }

            masterConfig.isBuild = config && config.isBuild;

            var parsed = text.parseName(name),
                nonStripName = parsed.moduleName +
                    (parsed.ext ? '.' + parsed.ext : ''),
                url = req.toUrl(nonStripName),
                useXhr = (masterConfig.useXhr) ||
                         text.useXhr;

            // Do not load if it is an empty: url
            if (url.indexOf('empty:') === 0) {
                onLoad();
                return;
            }

            //Load the text. Use XHR if possible and in a browser.
            if (!hasLocation || useXhr(url, defaultProtocol, defaultHostName, defaultPort)) {
                text.get(url, function (content) {
                    text.finishLoad(name, parsed.strip, content, onLoad);
                }, function (err) {
                    if (onLoad.error) {
                        onLoad.error(err);
                    }
                });
            } else {
                //Need to fetch the resource across domains. Assume
                //the resource has been optimized into a JS module. Fetch
                //by the module name + extension, but do not include the
                //!strip part to avoid file system issues.
                req([nonStripName], function (content) {
                    text.finishLoad(parsed.moduleName + '.' + parsed.ext,
                                    parsed.strip, content, onLoad);
                });
            }
        },

        write: function (pluginName, moduleName, write, config) {
            if (buildMap.hasOwnProperty(moduleName)) {
                var content = text.jsEscape(buildMap[moduleName]);
                write.asModule(pluginName + "!" + moduleName,
                               "define(function () { return '" +
                                   content +
                               "';});\n");
            }
        },

        writeFile: function (pluginName, moduleName, req, write, config) {
            var parsed = text.parseName(moduleName),
                extPart = parsed.ext ? '.' + parsed.ext : '',
                nonStripName = parsed.moduleName + extPart,
                //Use a '.js' file name so that it indicates it is a
                //script that can be loaded across domains.
                fileName = req.toUrl(parsed.moduleName + extPart) + '.js';

            //Leverage own load() method to load plugin value, but only
            //write out values that do not have the strip argument,
            //to avoid any potential issues with ! in file names.
            text.load(nonStripName, req, function (value) {
                //Use own write() method to construct full module value.
                //But need to create shell that translates writeFile's
                //write() to the right interface.
                var textWrite = function (contents) {
                    return write(fileName, contents);
                };
                textWrite.asModule = function (moduleName, contents) {
                    return write.asModule(moduleName, fileName, contents);
                };

                text.write(pluginName, nonStripName, textWrite, config);
            }, config);
        }
    };

    if (masterConfig.env === 'node' || (!masterConfig.env &&
            typeof process !== "undefined" &&
            process.versions &&
            !!process.versions.node &&
            !process.versions['node-webkit'] &&
            !process.versions['atom-shell'])) {
        //Using special require.nodeRequire, something added by r.js.
        fs = require.nodeRequire('fs');

        text.get = function (url, callback, errback) {
            try {
                var file = fs.readFileSync(url, 'utf8');
                //Remove BOM (Byte Mark Order) from utf8 files if it is there.
                if (file[0] === '\uFEFF') {
                    file = file.substring(1);
                }
                callback(file);
            } catch (e) {
                if (errback) {
                    errback(e);
                }
            }
        };
    } else if (masterConfig.env === 'xhr' || (!masterConfig.env &&
            text.createXhr())) {
        text.get = function (url, callback, errback, headers) {
            var xhr = text.createXhr(), header;
            xhr.open('GET', url, true);

            //Allow plugins direct access to xhr headers
            if (headers) {
                for (header in headers) {
                    if (headers.hasOwnProperty(header)) {
                        xhr.setRequestHeader(header.toLowerCase(), headers[header]);
                    }
                }
            }

            //Allow overrides specified in config
            if (masterConfig.onXhr) {
                masterConfig.onXhr(xhr, url);
            }

            xhr.onreadystatechange = function (evt) {
                var status, err;
                //Do not explicitly handle errors, those should be
                //visible via console output in the browser.
                if (xhr.readyState === 4) {
                    status = xhr.status || 0;
                    if (status > 399 && status < 600) {
                        //An http 4xx or 5xx error. Signal an error.
                        err = new Error(url + ' HTTP status: ' + status);
                        err.xhr = xhr;
                        if (errback) {
                            errback(err);
                        }
                    } else {
                        callback(xhr.responseText);
                    }

                    if (masterConfig.onXhrComplete) {
                        masterConfig.onXhrComplete(xhr, url);
                    }
                }
            };
            xhr.send(null);
        };
    } else if (masterConfig.env === 'rhino' || (!masterConfig.env &&
            typeof Packages !== 'undefined' && typeof java !== 'undefined')) {
        //Why Java, why is this so awkward?
        text.get = function (url, callback) {
            var stringBuffer, line,
                encoding = "utf-8",
                file = new java.io.File(url),
                lineSeparator = java.lang.System.getProperty("line.separator"),
                input = new java.io.BufferedReader(new java.io.InputStreamReader(new java.io.FileInputStream(file), encoding)),
                content = '';
            try {
                stringBuffer = new java.lang.StringBuffer();
                line = input.readLine();

                // Byte Order Mark (BOM) - The Unicode Standard, version 3.0, page 324
                // http://www.unicode.org/faq/utf_bom.html

                // Note that when we use utf-8, the BOM should appear as "EF BB BF", but it doesn't due to this bug in the JDK:
                // http://bugs.sun.com/bugdatabase/view_bug.do?bug_id=4508058
                if (line && line.length() && line.charAt(0) === 0xfeff) {
                    // Eat the BOM, since we've already found the encoding on this file,
                    // and we plan to concatenating this buffer with others; the BOM should
                    // only appear at the top of a file.
                    line = line.substring(1);
                }

                if (line !== null) {
                    stringBuffer.append(line);
                }

                while ((line = input.readLine()) !== null) {
                    stringBuffer.append(lineSeparator);
                    stringBuffer.append(line);
                }
                //Make sure we return a JavaScript string and not a Java string.
                content = String(stringBuffer.toString()); //String
            } finally {
                input.close();
            }
            callback(content);
        };
    } else if (masterConfig.env === 'xpconnect' || (!masterConfig.env &&
            typeof Components !== 'undefined' && Components.classes &&
            Components.interfaces)) {
        //Avert your gaze!
        Cc = Components.classes;
        Ci = Components.interfaces;
        Components.utils['import']('resource://gre/modules/FileUtils.jsm');
        xpcIsWindows = ('@mozilla.org/windows-registry-key;1' in Cc);

        text.get = function (url, callback) {
            var inStream, convertStream, fileObj,
                readData = {};

            if (xpcIsWindows) {
                url = url.replace(/\//g, '\\');
            }

            fileObj = new FileUtils.File(url);

            //XPCOM, you so crazy
            try {
                inStream = Cc['@mozilla.org/network/file-input-stream;1']
                           .createInstance(Ci.nsIFileInputStream);
                inStream.init(fileObj, 1, 0, false);

                convertStream = Cc['@mozilla.org/intl/converter-input-stream;1']
                                .createInstance(Ci.nsIConverterInputStream);
                convertStream.init(inStream, "utf-8", inStream.available(),
                Ci.nsIConverterInputStream.DEFAULT_REPLACEMENT_CHARACTER);

                convertStream.readString(inStream.available(), readData);
                convertStream.close();
                inStream.close();
                callback(readData.value);
            } catch (e) {
                throw new Error((fileObj && fileObj.path || '') + ': ' + e);
            }
        };
    }
    return text;
});


define('text!tabs/homeTab.html',[],function () { return '<div id="home-tab">\r\n    <h3>Drop</h3>\r\n    <p>Drag and drop a HAR file anywhere on this page.</p>\r\n\r\n    <h3>Select</h3>\r\n    <p>Select a file from your computer: <input type="file" id="select-file" accept=".har"/></p>\r\n    \r\n    <h3>Paste</h3>\r\n    <p>Paste HAR content into the text box below and press <b>Load</b></p>\r\n    <textarea id="sourceEditor" class="sourceEditor" cols="90" rows="20"></textarea>\r\n    <table cellpadding="0" cellspacing="0">\r\n        <tr>\r\n            <td><button id="appendPreview">Load</button></td>\r\n        </tr>\r\n    </table>\r\n\r\n    <div id="file-drop-overlay" class="overlay">\r\n        <div class="overlay-content">\r\n            Drop file to inspect its contents\r\n        </div>\r\n\r\n    </div>\r\n</div>';});

define("preview/jsonSchema", [], function() {

//*************************************************************************************************

/** 
 * JSONSchema Validator - Validates JavaScript objects using JSON Schemas 
 *	(http://www.json.com/json-schema-proposal/)
 *
 * Copyright (c) 2007 Kris Zyp SitePen (www.sitepen.com)
 * Licensed under the MIT (MIT-LICENSE.txt) license.
To use the validator call JSONSchema.validate with an instance object and an optional schema object.
If a schema is provided, it will be used to validate. If the instance object refers to a schema (self-validating), 
that schema will be used to validate and the schema parameter is not necessary (if both exist, 
both validations will occur). 
The validate method will return an array of validation errors. If there are no errors, then an 
empty list will be returned. A validation error will have two properties: 
"property" which indicates which property had the error
"message" which indicates what the error was
 */

var JSONSchema = {
	validate : function(/*Any*/instance,/*Object*/schema) {
		// Summary:
		//  	To use the validator call JSONSchema.validate with an instance object and an optional schema object.
		// 		If a schema is provided, it will be used to validate. If the instance object refers to a schema (self-validating), 
		// 		that schema will be used to validate and the schema parameter is not necessary (if both exist, 
		// 		both validations will occur). 
		// 		The validate method will return an object with two properties:
		// 			valid: A boolean indicating if the instance is valid by the schema
		// 			errors: An array of validation errors. If there are no errors, then an 
		// 					empty list will be returned. A validation error will have two properties: 
		// 						property: which indicates which property had the error
		// 						message: which indicates what the error was
		//
		return this._validate(instance,schema,false);
	},
	checkPropertyChange : function(/*Any*/value,/*Object*/schema, /*String*/ property) {
		// Summary:
		// 		The checkPropertyChange method will check to see if an value can legally be in property with the given schema
		// 		This is slightly different than the validate method in that it will fail if the schema is readonly and it will
		// 		not check for self-validation, it is assumed that the passed in value is already internally valid.  
		// 		The checkPropertyChange method will return the same object type as validate, see JSONSchema.validate for 
		// 		information.
		//
		return this._validate(value,schema, property || "property");
	},
	_validate : function(/*Any*/instance,/*Object*/schema,/*Boolean*/ _changing) {
	
	var errors = [];
		// validate a value against a property definition
	function checkProp(value, schema, path,i){
		var l;
		path += path ? typeof i == 'number' ? '[' + i + ']' : typeof i == 'undefined' ? '' : '.' + i : i;
		function addError(message){
			errors.push({property:path,message:message});
		}
		
		if((typeof schema != 'object' || schema instanceof Array) && (path || typeof schema != 'function')){
			if(typeof schema == 'function'){
				if(!(value instanceof schema)){
					addError("is not an instance of the class/constructor " + schema.name);
				}
			}else if(schema){
				addError("Invalid schema/property definition " + schema);
			}
			return null;
		}
		if(_changing && schema.readonly){
			addError("is a readonly field, it can not be changed");
		}
		if(schema['extends']){ // if it extends another schema, it must pass that schema as well
			checkProp(value,schema['extends'],path,i);
		}
		// validate a value against a type definition
		function checkType(type,value){
			if(type){
				if(typeof type == 'string' && type != 'any' && 
						(type == 'null' ? value !== null : typeof value != type) && 
						!(value instanceof Array && type == 'array') &&
						!(type == 'integer' && value%1===0)){
					return [{property:path,message:(typeof value) + " value found, but a " + type + " is required"}];
				}
				if(type instanceof Array){
					var unionErrors=[];
					for(var j = 0; j < type.length; j++){ // a union type 
						if(!(unionErrors=checkType(type[j],value)).length){
							break;
						}
					}
					if(unionErrors.length){
						return unionErrors;
					}
				}else if(typeof type == 'object'){
					var priorErrors = errors;
					errors = []; 
					checkProp(value,type,path);
					var theseErrors = errors;
					errors = priorErrors;
					return theseErrors; 
				} 
			}
			return [];
		}
		if(value === undefined){
			if(!schema.optional){  
				addError("is missing and it is not optional");
			}
		}else{
			errors = errors.concat(checkType(schema.type,value));
			if(schema.disallow && !checkType(schema.disallow,value).length){
				addError(" disallowed value was matched");
			}
			if(value !== null){
				if(value instanceof Array){
					if(schema.items){
						if(schema.items instanceof Array){
							for(i=0,l=value.length; i<l; i++){
								errors.concat(checkProp(value[i],schema.items[i],path,i));
							}
						}else{
							for(i=0,l=value.length; i<l; i++){
								errors.concat(checkProp(value[i],schema.items,path,i));
							}
						}							
					}
					if(schema.minItems && value.length < schema.minItems){
						addError("There must be a minimum of " + schema.minItems + " in the array");
					}
					if(schema.maxItems && value.length > schema.maxItems){
						addError("There must be a maximum of " + schema.maxItems + " in the array");
					}
				}else if(schema.properties){
					errors.concat(checkObj(value,schema.properties,path,schema.additionalProperties));
				}
				if(schema.pattern && typeof value == 'string' && !value.match(schema.pattern)){
					addError("does not match the regex pattern " + schema.pattern);
				}
				if(schema.maxLength && typeof value == 'string' && value.length > schema.maxLength){
					addError("may only be " + schema.maxLength + " characters long");
				}
				if(schema.minLength && typeof value == 'string' && value.length < schema.minLength){
					addError("must be at least " + schema.minLength + " characters long");
				}
				if(typeof schema.minimum !== undefined && typeof value == typeof schema.minimum && 
						schema.minimum > value){
					addError("must have a minimum value of " + schema.minimum);
				}
				if(typeof schema.maximum !== undefined && typeof value == typeof schema.maximum && 
						schema.maximum < value){
					addError("must have a maximum value of " + schema.maximum);
				}
				if(schema['enum']){
					var enumer = schema['enum'];
					l = enumer.length;
					var found;
					for(var j = 0; j < l; j++){
						if(enumer[j]===value){
							found=1;
							break;
						}
					}
					if(!found){
						addError("does not have a value in the enumeration " + enumer.join(", "));
					}
				}
				if(typeof schema.maxDecimal == 'number' && 
					(value.toString().match(new RegExp("\\.[0-9]{" + (schema.maxDecimal + 1) + ",}")))){
					addError("may only have " + schema.maxDecimal + " digits of decimal places");
				}
			}
		}
		return null;
	}
	// validate an object against a schema
	function checkObj(instance,objTypeDef,path,additionalProp){
	
		if(typeof objTypeDef =='object'){
			if(typeof instance != 'object' || instance instanceof Array){
				errors.push({property:path,message:"an object is required"});
			}
			
			for(var i in objTypeDef){ 
				if(objTypeDef.hasOwnProperty(i) && !(i.charAt(0) == '_' && i.charAt(1) == '_')){
					var value = instance[i];
					var propDef = objTypeDef[i];
					checkProp(value,propDef,path,i);
				}
			}
		}
		for(i in instance){
			if(instance.hasOwnProperty(i) && !(i.charAt(0) == '_' && i.charAt(1) == '_') && objTypeDef && !objTypeDef[i] && additionalProp===false){
				errors.push({property:path,message:(typeof value) + "The property " + i +
						" is not defined in the schema and the schema does not allow additional properties"});
			}
			var requires = objTypeDef && objTypeDef[i] && objTypeDef[i].requires;
			if(requires && !(requires in instance)){
				errors.push({property:path,message:"the presence of the property " + i + " requires that " + requires + " also be present"});
			}
			value = instance[i];
			if(objTypeDef && typeof objTypeDef == 'object' && !(i in objTypeDef)){
				checkProp(value,additionalProp,path,i); 
			}
			if(!_changing && value && value.$schema){
				errors = errors.concat(checkProp(value,value.$schema,path,i));
			}
		}
		return errors;
	}
	if(schema){
		checkProp(instance,schema,'',_changing || '');
	}
	if(!_changing && instance && instance.$schema){
		checkProp(instance,instance.$schema,'','');
	}
	return {valid:!errors.length,errors:errors};
	}
	/* will add this later
	newFromSchema : function() {
	}
*/
}

return JSONSchema;

//*************************************************************************************************
});

/* This file comes from DOJO (adapted for requirejs): dojox/json/ref.js */

define("preview/ref", [
    "core/lib"
],

function(Lib) {

//*************************************************************************************************

// summary:
// Adds advanced JSON {de}serialization capabilities to the base json library.
// This enhances the capabilities of dojo.toJson and dojo.fromJson,
// adding referencing support, date handling, and other extra format handling.
// On parsing, references are resolved. When references are made to
// ids/objects that have been loaded yet, the loader function will be set to
// _loadObject to denote a lazy loading (not loaded yet) object. 

var ref =
{
	resolveJson: function(/*Object*/ root,/*Object?*/ args){
		// summary:
		// 		Indexes and resolves references in the JSON object.
		// description:
		// 		A JSON Schema object that can be used to advise the handling of the JSON (defining ids, date properties, urls, etc)
		//
		// root:
		//		The root object of the object graph to be processed
		// args:
		//		Object with additional arguments:
		//
		// The *index* parameter.
		//		This is the index object (map) to use to store an index of all the objects. 
		// 		If you are using inter-message referencing, you must provide the same object for each call.
		// The *defaultId* parameter.
		//		This is the default id to use for the root object (if it doesn't define it's own id)
		//	The *idPrefix* parameter.
		//		This the prefix to use for the ids as they enter the index. This allows multiple tables 
		// 		to use ids (that might otherwise collide) that enter the same global index. 
		// 		idPrefix should be in the form "/Service/".  For example,
		//		if the idPrefix is "/Table/", and object is encountered {id:"4",...}, this would go in the
		//		index as "/Table/4".
		//	The *idAttribute* parameter.
		//		This indicates what property is the identity property. This defaults to "id"
		//	The *assignAbsoluteIds* parameter.
		//		This indicates that the resolveJson should assign absolute ids (__id) as the objects are being parsed.
		//  
		// The *schemas* parameter
		//		This provides a map of schemas, from which prototypes can be retrieved
		// The *loader* parameter
		//		This is a function that is called added to the reference objects that can't be resolved (lazy objects)
		// return:
		//		An object, the result of the processing
		args = args || {};
		var idAttribute = args.idAttribute || 'id';
		var prefix = args.idPrefix || ''; 
		var assignAbsoluteIds = args.assignAbsoluteIds;
		var index = args.index || {}; // create an index if one doesn't exist
		var timeStamps = args.timeStamps;
		var ref,reWalk=[];
		var pathResolveRegex = /^(.*\/)?(\w+:\/\/)|[^\/\.]+\/\.\.\/|^.*\/(\/)/;
		var addProp = this._addProp;
		var F = function(){};
		function walk(it, stop, defaultId, schema, defaultObject){
			// this walks the new graph, resolving references and making other changes
		 	var update, val, id = idAttribute in it ? it[idAttribute] : defaultId;
		 	if(id !== undefined){
		 		id = (prefix + id).replace(pathResolveRegex,'$2$3');
		 	}
		 	var target = defaultObject || it;
			if(id !== undefined){ // if there is an id available...
				if(assignAbsoluteIds){
					it.__id = id;
				}
				if(args.schemas && (!(it instanceof Array)) && // won't try on arrays to do prototypes, plus it messes with queries 
		 					(val = id.match(/^(.+\/)[^\.\[]*$/))){ // if it has a direct table id (no paths)
		 			schema = args.schemas[val[1]];
				} 
				// if the id already exists in the system, we should use the existing object, and just 
				// update it... as long as the object is compatible
				if(index[id] && ((it instanceof Array) == (index[id] instanceof Array))){ 
					target = index[id];
					delete target.$ref; // remove this artifact
					update = true;
				}else{
				 	var proto = schema && schema.prototype; // and if has a prototype
					if(proto){
						// if the schema defines a prototype, that needs to be the prototype of the object
						F.prototype = proto;
						target = new F();
					}
				}
				index[id] = target; // add the prefix, set _id, and index it
				if(timeStamps){
					timeStamps[id] = args.time;
				}
			}
			var properties = schema && schema.properties; 
			var length = it.length;
			for(var i in it){
				if(i==length){
					break;		
				}
				if(it.hasOwnProperty(i)){
					val=it[i];
					var propertyDefinition = properties && properties[i];
					if(propertyDefinition && propertyDefinition.format == 'date-time' && typeof val == 'string'){
						val = Lib.fromISOString(val);
					}else if((typeof val =='object') && val && !(val instanceof Date)){
						ref=val.$ref;
						if(ref){ // a reference was found
							// make sure it is a safe reference
							delete it[i];// remove the property so it doesn't resolve to itself in the case of id.propertyName lazy values
							var path = ref.replace(/(#)([^\.\[])/,'$1.$2').match(/(^([^\[]*\/)?[^#\.\[]*)#?([\.\[].*)?/); // divide along the path
							if((ref = (path[1]=='$' || path[1]=='this' || path[1]=='') ? root : index[(prefix + path[1]).replace(pathResolveRegex,'$2$3')])){  // a $ indicates to start with the root, otherwise start with an id
								// if there is a path, we will iterate through the path references
								if(path[3]){
									path[3].replace(/(\[([^\]]+)\])|(\.?([^\.\[]+))/g,function(t,a,b,c,d){
										ref = ref && ref[b ? b.replace(/[\"\'\\]/,'') : d];
									});
								}
							}
							if(ref){
								// otherwise, no starting point was found (id not found), if stop is set, it does not exist, we have
								// unloaded reference, if stop is not set, it may be in a part of the graph not walked yet,
								// we will wait for the second loop
								val = ref;
							}else{
								if(!stop){
									var rewalking;
									if(!rewalking){
										reWalk.push(target); // we need to rewalk it to resolve references
									}
									rewalking = true; // we only want to add it once
								}else{
									val = walk(val, false, val.$ref, propertyDefinition);
									// create a lazy loaded object
									val._loadObject = args.loader;
								}
							}
						}else{
							if(!stop){ // if we are in stop, that means we are in the second loop, and we only need to check this current one,
								// further walking may lead down circular loops
								val = walk(
									val,
									reWalk==it,
									id && addProp(id, i), // the default id to use
									propertyDefinition, 
									// if we have an existing object child, we want to 
									// maintain it's identity, so we pass it as the default object
									target != it && typeof target[i] == 'object' && target[i] 
								);
							}
						}
					}
					it[i] = val;
					if(target!=it && !target.__isDirty){// do updates if we are updating an existing object and it's not dirty				
						var old = target[i];
						target[i] = val; // only update if it changed
						if(update && val !== old && // see if it is different 
								!target._loadObject && // no updates if we are just lazy loading 
								!(val instanceof Date && old instanceof Date && val.getTime() == old.getTime()) && // make sure it isn't an identical date
								!(typeof val == 'function' && typeof old == 'function' && val.toString() == old.toString()) && // make sure it isn't an indentical function
								index.onUpdate){
							index.onUpdate(target,i,old,val); // call the listener for each update
						}
					}
				}
			}
	
			if(update){
				// this means we are updating, we need to remove deleted
				for(i in target){
					if(!target.__isDirty && target.hasOwnProperty(i) && !it.hasOwnProperty(i) && i != '__id' && i != '__clientId' && !(target instanceof Array && isNaN(i))){
						if(index.onUpdate && i != "_loadObject" && i != "_idAttr"){
							index.onUpdate(target,i,target[i],undefined); // call the listener for each update
						}
						delete target[i];
						while(target instanceof Array && target.length && target[target.length-1] === undefined){
							// shorten the target if necessary
							target.length--;
						}
					}
				}
			}else{
				if(index.onLoad){
					index.onLoad(target);
				}
			}
			return target;
		}
		if(root && typeof root == 'object'){
			root = walk(root,false,args.defaultId); // do the main walk through
			walk(reWalk,false); // re walk any parts that were not able to resolve references on the first round
		}
		return root;
	},
	
	_addProp: function(id, prop){
		return id + (id.match(/#/) ? id.length == 1 ? '' : '.' : '#') + prop;
	}
}

return ref;

//*************************************************************************************************
});

/* See license.txt for terms of usage */

define("preview/harSchema", [], function() {

// ************************************************************************************************
// HAR Schema Definition

// Date time fields use ISO8601 (YYYY-MM-DDThh:mm:ss.sTZD, e.g. 2009-07-24T19:20:30.45+01:00)
var dateTimePattern = /^(\d{4})(-)?(\d\d)(-)?(\d\d)(T)?(\d\d)(:)?(\d\d)(:)?(\d\d)(\.\d+)?(Z|([+-])(\d\d)(:)?(\d\d))/;

/**
 * Root HTML Archive type.
 */
var logType = {
    "logType": {
        "id": "logType",
        "description": "HTTP Archive structure.",
        "type": "object",
        "properties": {
            "log": {
                "type": "object",
                "properties": {
                    "version": {"type": "string"},
                    "creator": {"$ref": "creatorType"},
                    "browser": {"$ref": "browserType"},
                    "pages": {"type": "array", "optional": true, "items": {"$ref": "pageType"}},
                    "entries": {"type": "array", "items": {"$ref": "entryType"}},
                    "comment": {"type": "string", "optional": true}
                }
            }
        }
    }
};

var creatorType = {
    "creatorType": {
        "id": "creatorType",
        "description": "Name and version info of the log creator app.",
        "type": "object",
        "properties": {
            "name": {"type": "string"},
            "version": {"type": "string"},
            "comment": {"type": "string", "optional": true}
        }
    }
};

var browserType = {
    "browserType": {
        "id": "browserType",
        "description": "Name and version info of used browser.",
        "type": "object",
        "optional": true,
        "properties": {
            "name": {"type": "string"},
            "version": {"type": "string"},
            "comment": {"type": "string", "optional": true}
        }
    }
};

var pageType = {
    "pageType": {
        "id": "pageType",
        "description": "Exported web page",
        "optional": true,
        "properties": {
            "startedDateTime": {"type": "string", "format": "date-time", "pattern": dateTimePattern},
            "id": {"type": "string", "unique": true},
            "title": {"type": "string"},
            "pageTimings": {"$ref": "pageTimingsType"},
            "comment": {"type": "string", "optional": true}
        }
    }
};

var pageTimingsType = {
    "pageTimingsType": {
        "id": "pageTimingsType",
        "description": "Timing info about page load",
        "properties": {
            "onContentLoad": {"type": "number", "optional": true, "min": -1},
            "onLoad": {"type": "number", "optional": true, "min": -1},
            "comment": {"type": "string", "optional": true}
        }
    }
};

var entryType = {
    "entryType": {
        "id": "entryType",
        "description": "Request and Response related info",
        "optional": true,
        "properties": {
            "pageref": {"type": "string", "optional": true},
            "startedDateTime": {"type": "string", "format": "date-time", "pattern": dateTimePattern},
            "time": {"type": "number", "min": 0},
            "request" : {"$ref": "requestType"},
            "response" : {"$ref": "responseType"},
            "cache" : {"$ref": "cacheType"},
            "timings" : {"$ref": "timingsType"},
            "serverIPAddress" : {"type": "string", "optional": true},
            "connection" : {"type": "string", "optional": true},
            "comment": {"type": "string", "optional": true}
        }
    }
};

var requestType = {
    "requestType": {
        "id": "requestType",
        "description": "Monitored request",
        "properties": {
            "method": {"type": "string"},
            "url": {"type": "string"},
            "httpVersion": {"type" : "string"},
            "cookies" : {"type": "array", "items": {"$ref": "cookieType"}},
            "headers" : {"type": "array", "items": {"$ref": "recordType"}},
            "queryString" : {"type": "array", "items": {"$ref": "recordType"}},
            "postData" : {"$ref": "postDataType"},
            "headersSize" : {"type": "integer"},
            "bodySize" : {"type": "integer"},
            "comment": {"type": "string", "optional": true}
        }
    }
};

var recordType = {
    "recordType": {
        "id": "recordType",
        "description": "Helper name-value pair structure.",
        "properties": {
            "name": {"type": "string"},
            "value": {"type": "string"},
            "comment": {"type": "string", "optional": true}
        }
    }
};

var responseType = {
    "responseType": {
        "id": "responseType",
        "description": "Monitored Response.",
        "properties": {
            "status": {"type": "integer"},
            "statusText": {"type": "string"},
            "httpVersion": {"type": "string"},
            "cookies" : {"type": "array", "items": {"$ref": "cookieType"}},
            "headers" : {"type": "array", "items": {"$ref": "recordType"}},
            "content" : {"$ref": "contentType"},
            "redirectURL" : {"type": "string"},
            "headersSize" : {"type": "integer"},
            "bodySize" : {"type": "integer"},
            "comment": {"type": "string", "optional": true}
        }
    }
};

var cookieType = {
    "cookieType": {
        "id": "cookieType",
        "description": "Cookie description.",
        "properties": {
            "name": {"type": "string"},
            "value": {"type": "string"},
            "path": {"type": "string", "optional": true},
            "domain" : {"type": "string", "optional": true},
            "expires" : {"type": "string", "optional": true},
            "httpOnly" : {"type": "boolean", "optional": true},
            "secure" : {"type": "boolean", "optional": true},
            "comment": {"type": "string", "optional": true}
        }
    }
}

var postDataType = {
    "postDataType": {
        "id": "postDataType",
        "description": "Posted data info.",
        "optional": true,
        "properties": {
            "mimeType": {"type": "string"},
            "text": {"type": "string", "optional": true},
            "params": {
                "type": "array",
                "optional": true,
                "properties": {
                    "name": {"type": "string"},
                    "value": {"type": "string", "optional": true},
                    "fileName": {"type": "string", "optional": true},
                    "contentType": {"type": "string", "optional": true},
                    "comment": {"type": "string", "optional": true}
                }
            },
            "comment": {"type": "string", "optional": true}
        }
    }
};

var contentType = {
    "contentType": {
        "id": "contentType",
        "description": "Response content",
        "properties": {
            "size": {"type": "integer"},
            "compression": {"type": "integer", "optional": true},
            "mimeType": {"type": "string"},
            "text": {"type": "string", "optional": true},
            "encoding": {"type": "string", "optional": true},
            "comment": {"type": "string", "optional": true}
        }
    }
};

var cacheType = {
    "cacheType": {
        "id": "cacheType",
        "description": "Info about a response coming from the cache.",
        "properties": {
            "beforeRequest": {"$ref": "cacheEntryType"},
            "afterRequest": {"$ref": "cacheEntryType"},
            "comment": {"type": "string", "optional": true}
        }
    }
};

var cacheEntryType = {
    "cacheEntryType": {
        "id": "cacheEntryType",
        "optional": true,
        "description": "Info about cache entry.",
        "properties": {
            "expires": {"type": "string", optional: "true"},
            "lastAccess": {"type": "string"},
            "eTag": {"type": "string"},
            "hitCount": {"type": "integer"},
            "comment": {"type": "string", "optional": true}
        }
    }
};

var timingsType = {
    "timingsType": {
        "id": "timingsType",
        "description": "Info about request-response timing.",
        "properties": {
            "dns": {"type": "number", "optional": true, "min": -1},
            "connect": {"type": "number", "optional": true, "min": -1},
            "blocked": {"type": "number", "optional": true, "min": -1},
            "send": {"type": "number", "min": -1},
            "wait": {"type": "number", "min": -1},
            "receive": {"type": "number", "min": -1},
            "ssl": {"type": "number", "optional": true, "min": -1},
            "comment": {"type": "string", "optional": true}
        }
    }
};

// ************************************************************************************************
// Helper schema object

function Schema() {}
Schema.prototype =
{
    registerType: function()
    {
        var doIt = function(my, obj){
            for (var name in obj) {
                if (obj.hasOwnProperty(name) && name != "prototype") {
                    my[name] = obj[name];
                }
            }
        }
        var that = this;
        for(i=0; i < arguments.length; i +=1) {
            doIt(that, arguments[i]);
        };
    }
};

// ************************************************************************************************
// Registration

// Register all defined types into the final schema object.
var schema = new Schema();
schema.registerType(
    logType,
    creatorType,
    browserType,
    pageType,
    pageTimingsType,
    entryType,
    requestType,
    recordType,
    responseType,
    postDataType,
    contentType,
    cacheType,
    cacheEntryType,
    timingsType
);

// ************************************************************************************************

return schema;

// ************************************************************************************************
});

/* See license.txt for terms of usage */

/**
 * @module core/cookies
 */
define('core/cookies',[
    "core/lib"
],

function(Lib) {

//*************************************************************************************************

/**
 * Helper functions for handling cookies.
 * @alias module:core/cookies
 */
var Cookies =
{
    /**
     * @param {String} name The name of the cookie to get the value of.
     * @return {String} The cookie value if found, else `null` if the cookie exists but has no
     *     value, else `undefined`.
     */
    getCookie: function(name)
    {
        var cookies = document.cookie.split(";");
        for (var i= 0; i<cookies.length; i++)
        {
            var cookie = cookies[i].split("=");
            if (Lib.trim(cookie[0]) == name)
                return cookie[1].length ? unescape(Lib.trim(cookie[1])) : null;
        }
    },

    /**
     * Sets a cookie.
     * @param {String} name The name of the cookie.
     * @param {String} value The value of the cookie.
     * @param {Number} expires Cookie expiry in days.
     * @param {String} path The path of the cookie.
     * @param {String} domain The domain of the cookie.
     * @param {Boolean} secure Is the cookie secure?
     */
    setCookie: function(name, value, expires, path, domain, secure)
    {
        var today = new Date();
        today.setTime(today.getTime());

        if (expires)
            expires = expires * 1000 * 60 * 60 * 24;

        var expiresDate = new Date(today.getTime() + expires);
        document.cookie = name + "=" + escape(value) +
            (expires ? ";expires=" + expiresDate.toGMTString() : "") +
            (path ? ";path=" + path : "") +
            (domain ? ";domain=" + domain : "") +
            (secure ? ";secure" : "");
    },

    /**
     * Removes a cookie, by setting its expiry to a date in the past.
     * @param {String} name The name of the cookie.
     * @param {String} path The path of the cookie.
     * @param {String} domain The domain of the cookie.
     */
    removeCookie: function(name, path, domain)
    {
        if (this.getCookie(name))
        {
            document.cookie = name + "=" +
                (path ? ";path=" + path : "") +
                (domain ? ";domain=" + domain : "") +
                ";expires=Thu, 01-Jan-1970 00:00:01 GMT";
        }
    },

    toggleCookie: function(name)
    {
        var value = this.getBooleanCookie(name);
        this.setCookie(name, !value);
    },

    /**
     * Returns a `true`/`false` value for the cookie with name, `name`.
     * @param {String} name The name of the cookie.
     * @return {Boolean} `false` if the cookie does not exist, or has a value == `"false"`,
     *     else `true`.
     */
    getBooleanCookie: function(name)
    {
        var value = this.getCookie(name);
        return (!value || value == "false") ? false : true;
    },

    /**
     * Sets a `true`/`false` cookie value for the cookie with name, `name`.
     * @param {String} name The name of the cookie.
     * @param {Object} value The value to set.  If truthy, the cookie value will be set to
     *     `"true"`, else it will be set to `"false"`.
     */
    setBooleanCookie: function(name, value)
    {
        this.setCookie(name, value ? "true" : "false");
    }
};

return Cookies;

//*************************************************************************************************
});

/* See license.txt for terms of usage */

define(
'nls/harModel',{
    "root": {
        "validationType": "HAR Validation",
        "validationSumTimeError": "Sum of request timings doesn't correspond to the total value: " +
            "%S (request.time: %S vs. sum: %S), request#: %S, parent page: %S",
        "validationNegativeTimeError": "Negative time is not allowed: " +
            "%S, request#: %S, parent page: %S"
    }
});


/* See license.txt for terms of usage */

/**
 * @module preview/harModel
 */
define('preview/harModel',[
    "core/lib",
    "preview/jsonSchema",
    "preview/ref",
    "preview/harSchema",
    "core/cookies",
    "core/trace",
    "i18n!nls/harModel"
],

function(Lib, JSONSchema, Ref, HarSchema, Cookies, Trace, Strings) {

//*************************************************************************************************
// Statistics

/**
 * @constructor
 * @alias module:preview/harModel
 */
function HarModel()
{
    this.input = null;
}

HarModel.prototype =
/** @lends module:preview/harModel.prototype */
{
    append: function(input)
    {
        if (!input)
        {
            Trace.error("HarModel.append; Trying to append null input!");
            return;
        }

        // Sort all requests according to the start time.
        input.log.entries.sort(function(a, b)
        {
            var timeA = Lib.parseISO8601(a.startedDateTime);
            var timeB = Lib.parseISO8601(b.startedDateTime);

            if (timeA < timeB)
                return -1;
            else if (timeA > timeB)
                return 1;

            return 0;
        })

        if (this.input)
        {
            if (input.log.pages)
            {
                for (var i=0; i<input.log.pages.length; i++)
                    this.importPage(input.log.pages[i], input.log.entries);
            }
            else
            {
                Trace.error("Import of additional data without a page is not yet supported.");
                //xxxHonza: how to properly import data with no page?
                //for (var i=0; i<input.log.entries.length; i++)
                //    this.input.log.entries.push(input.log.entries[i]);
                return null;
            }
        }
        else
        {
            this.input = Lib.cloneJSON(input);
        }

        return this.input;
    },

    // * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
    // Pages

    /**
     * @return {Array} An array of page objects.
     */
    getPages: function()
    {
        if (!this.input)
            return [];

        return this.input.log.pages ? this.input.log.pages : [];
    },

    /**
     * @return {Page} The first page if it exists, else null.
     */
    getFirstPage: function()
    {
        var pages = this.getPages();
        return pages.length > 0 ? pages[0] : null;
    },

    /**
     * @see {@link module:preview/harModel.getPageEntries}
     */
    getPageEntries: function(page)
    {
        return HarModel.getPageEntries(this.input, page);
    },

    getAllEntries: function(page)
    {
        return this.input ? this.input.log.entries : [];
    },

    getParentPage: function(file)
    {
        return HarModel.getParentPage(this.input, file);
    },

    importPage: function(page, entries)
    {
        var pageId = this.getUniquePageID(page.id);
        var prevPageId = page.id;
        page.id = pageId;

        this.input.log.pages.push(page);
        for (var i=0; i<entries.length; i++)
        {
            var entry = entries[i];
            if (entry.pageref == prevPageId)
            {
                entry.pageref = pageId;
                this.input.log.entries.push(entry);
            }
        }
    },

    getUniquePageID: function(defaultId)
    {
        var pages = this.input.log.pages;
        var hashTable = {};
        for (var i=0; i<pages.length; i++)
            hashTable[pages[i].id] = true;

        if (!hashTable[defaultId])
            return defaultId;

        var counter = 1;
        while (true)
        {
            var pageId = defaultId + counter;
            if (!hashTable[pageId])
                return pageId;
            counter++;
        }
    },

    // * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
    // JSON

    toJSON : function(input)
    {
        if (!input)
            input = this.input;

        if (!input)
            return "";

        // xxxHonza: we don't have to iterate all entries again if it did already.
        var entries = this.input.log.entries;
        for (var i=0; i<entries.length; i++) {
            var entry = entries[i];
            if (entry.response.content.text)
                entry.response.content.toJSON = contentToUnicode;
        }

        var jsonString = JSON.stringify(this.input, null, "\t");
        var result = jsonString.replace(/\\\\u/g, "\\u");
        return result;
    },

    // * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
    // Statistics

    getSize: function(input)
    {
        if (!input)
            input = this.input;

        if (!input)
            return 0;

        var jsonString = dojo.toJson(input, true);
        return jsonString.length;
    }
};

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
// Static methods (no instance of the model, no |this| )

HarModel.parse = function(jsonString, validate)
{
    var input = jsonString;

    try
    {
        if (typeof(jsonString) === "string")
            input = jQuery.parseJSON(jsonString);
    }
    catch (err)
    {
        console.exception("HarModel.parse; EXCEPTION", err);

        throw {
            errors: [{
                "message": "Failed to parse JSON",
                "property": "JSON evaluation"
            }]
        };
    }

    if (!validate)
        return input;

    //xxxHonza: the schema doesn't have to be resolved repeatedly.
    var resolvedSchema = Ref.resolveJson(HarSchema);
    var result = JSONSchema.validate(input, resolvedSchema.logType);
    if (result.valid)
    {
        this.validateRequestTimings(input);
        return input;
    }


    throw result;
};

// xxxHonza: optimalization using a map?
/**
 * If `page` is not provided, then return all the HAR entries without a parent `Page`.
 * If `page` is provided, then return all the HAR entries whose `pageref` matches `page.id`.
 * @param {HAR} input The input HAR object.
 * @param {Page} page The `Page` object to use to search for entries.
 * @return {Array} The `Page` entries.
 */
HarModel.getPageEntries = function(input, page)
{
    var result = [];

    var entries = input.log.entries;
    if (!entries)
        return result;

    for (var i=0; i<entries.length; i++)
    {
        var entry = entries[i];

        // Return all requests that doesn't have a parent page.
        if (!entry.pageref && !page)
            result.push(entry);

        // Return all requests for the specified page.
        if (page && entry.pageref == page.id)
            result.push(entry);
    }

    return result;
};

// xxxHonza: optimize using a map?
/**
 * @param {HAR} input The input HAR object.
 * @param {Entry} file The `Entry` object to use to find the parent `Page`.
 * @return {Page} The parent `Page` of the file/`Entry`, or null if a parent `Page` could not be
 *     found.
 */
HarModel.getParentPage = function(input, file)
{
    var pages = input.log.pages;
    if (!pages)
        return null;

    for (var i=0; i<pages.length; i++)
    {
        if (pages[i].id == file.pageref)
            return pages[i];
    }

    return null;
};

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
// Validation

HarModel.validateRequestTimings = function(input)
{
    var errors = [];

    // Iterate all request timings and check the total time.
    var entries = input.log.entries;
    for (var i=0; i<entries.length; i++)
    {
        var entry = entries[i];
        var timings = entry.timings;

        /* http://code.google.com/p/chromium/issues/detail?id=339551
        var total = 0;
        for (var p in timings)
        {
            var time = parseInt(timings[p], 10);

            // According to the spec, the ssl time is alrady included in "connect".
            if (p != "ssl" && time > 0)
                total += time;
        }

        if (total != entry.time)
        {
            var message = Lib.formatString(Strings.validationSumTimeError,
                entry.request.url, entry.time, total, i, entry.pageref);

            errors.push({
                input: input,
                file: entry,
                "message": message,
                "property": Strings.validationType
            });
        }*/

        if (timings.blocked < -1 ||
            timings.connect < -1 ||
            timings.dns < -1 ||
            timings.receive < -1 ||
            timings.send < -1 ||
            timings.wait < -1)
        {
            var message = Lib.formatString(Strings.validationNegativeTimeError,
                entry.request.url, i, entry.pageref);

            errors.push({
                input: input,
                file: entry,
                "message": message,
                "property": Strings.validationType
            });
        }
    }

    if (errors.length)
        throw {errors: errors, input: input};
}

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

/**
 * Helper for loading HAR resources.
 * @namespace
 */
HarModel.Loader =
{
    run: function(callback, errorCallback)
    {
        var baseUrl = Lib.getURLParameter("baseUrl");

        // Append traling slahs if missing.
        if (baseUrl && baseUrl[baseUrl.length-1] != "/")
            baseUrl += "/";

        var paths = Lib.getURLParameters("path");
        var callbackName = Lib.getURLParameter("callback");
        var inputUrls = Lib.getURLParameters("inputUrl").concat(Lib.getHashParameters("inputUrl"));

        //for (var p in inputUrls)
        //    inputUrls[p] = inputUrls[p].replace(/%/g,'%25');

        var urls = [];
        for (var p in paths)
            urls.push(baseUrl ? baseUrl + paths[p] : paths[p]);

        // Load input data (using JSONP) from remote location.
        // http://domain/har/viewer?inputUrl=<remote-file-url>&callback=<name-of-the-callback>
        for (var p in inputUrls)
            urls.push(inputUrls[p]);

        if ((baseUrl || inputUrls.length > 0) && urls.length > 0)
            return this.loadRemoteArchive(urls, callbackName, callback, errorCallback);

        // The URL can specify also a locale file (with the same domain).
        // http://domain/har/viewer?path=<local-file-path>
        var filePath = Lib.getURLParameter("path");
        if (filePath)
            return this.loadLocalArchive(filePath, callback, errorCallback);
    },

    /**
     * Loads the HAR from `path` by navigating to a new URL.
     * @param {String} path The path to the example.
     * @param {Function} callback Not used.
     */
    loadExample: function(path, callback)
    {
        var href = document.location.href;
        var index = href.indexOf("?");
        document.location = href.substr(0, index) + "?path=" + path;

        // Show timeline and stats by default if an example is displayed.
        Cookies.setCookie("timeline", true);
        Cookies.setCookie("stats", true);
    },

    /**
     * Loads the HAR from `filePath` as JSON using Ajax.
     * @param {String} filePath The path to the HAR.
     * @param {Function} callback Called when load is successful.
     * @param {Function} errorCallback Called when load fails.
     */
    loadLocalArchive: function(filePath, callback, errorCallback)
    {
        // Execute XHR to get a local file (the same domain).
        $.ajax({
            url: filePath,
            context: this,
            dataType: "json",

            success: function(response)
            {
                callback(response);
            },

            error: function(jqXHR, textStatus, errorThrown)
            {
                errorCallback(jqXHR, textStatus, errorThrown);
            }
        });

        return true;
    },

    loadRemoteArchive: function(urls, callbackName, callback, errorCallback)
    {
        if (!urls.length)
            return false;

        // Get the first URL in the queue.
        var url = urls.shift();

        if (!callbackName)
            callbackName = "onInputData";

        $.ajax({
            url: url,
            context: this,
            dataType: "jsonp",
            jsonp: "callback",
            jsonpCallback: callbackName,

            success: function(response)
            {
                if (callback)
                    callback(response);

                // Asynchronously load other HAR files (jQuery doesn't like is synchronously).
                // The timeout specifies how much the browser UI cane be frozen.
                if (urls.length)
                {
                    var self = this;
                    setTimeout(function() {
                        self.loadRemoteArchive(urls, callbackName, callback, errorCallback);
                    }, 300);
                }
            },

            error: function(jqXHR, textStatus, errorThrown)
            {
                if (errorCallback)
                    errorCallback(jqXHR, textStatus, errorThrown);
            }
        });

        return true;
    },

    load: function(scope, url, crossDomain, callbackName, callback, errorCallback)
    {
        function onLoaded(input)
        {
            if (scope.appendPreview)
                scope.appendPreview(input);

            if (callback)
                callback.call(scope, input);
        }

        function onError(jqXHR, textStatus, errorThrown)
        {
            if (scope.onLoadError)
                scope.onLoadError(jqXHR, textStatus, errorThrown);

            if (errorCallback)
                errorCallback.call(scope, jqXHR, textStatus, errorThrown);
        }

        if (crossDomain)
            return this.loadRemoteArchive([url], callbackName, onLoaded, onError);
        else
            return this.loadLocalArchive(url, onLoaded, onError);
    }
};

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

// Make sure the response (it can be binary) is converted to Unicode.
function contentToUnicode()
{
    var newContent = {};
    for (var prop in this) {
        if (prop != "toJSON")
            newContent[prop] = this[prop];
    }

    if (!this.text)
        return newContent;

    newContent.text = Array.prototype.map.call(this.text, function(x) {
        var charCode = x.charCodeAt(0);
        if ((charCode >= 0x20 && charCode < 0x7F) ||
             charCode == 0xA || charCode == 0xD)
            return x.charAt(0);

        var unicode = charCode.toString(16).toUpperCase();
        while (unicode.length < 4)
            unicode = "0" + unicode;
        return "\\u" + unicode;
    }).join("");

    return newContent;
}

return HarModel;

//*************************************************************************************************
});

/*
Copyright 2012 Google Inc.
Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at
     http://www.apache.org/licenses/LICENSE-2.0
Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
Author: Eric Bidelman (<EMAIL>)

Modifications made by: Tim Hemming
*/
define("dnd", [],function () {
    return function DnDFileController(selector, onDropCallback, enterCallback, leaveCallback) {
        var el_ = document.querySelector(selector);
        var overCount = 0;

        this.dragenter = function (e) {
            e.stopPropagation();
            e.preventDefault();
            overCount++;
            el_.classList.add('dropping');
            if (typeof enterCallback === 'function') {
                enterCallback(e);
            }
        };

        this.dragover = function (e) {
            e.stopPropagation();
            e.preventDefault();
        };

        this.dragleave = function (e) {
            e.stopPropagation();
            e.preventDefault();
            if (--overCount <= 0) {
                el_.classList.remove('dropping');
                overCount = 0;
                if (typeof leaveCallback === 'function') {
                    leaveCallback(e);
                }
            }
        };

        this.drop = function (e) {
            e.stopPropagation();
            e.preventDefault();

            overCount = 0;
            el_.classList.remove('dropping');
            if (typeof leaveCallback === 'function') {
                leaveCallback(e);
            }

            onDropCallback(e.dataTransfer);
        };

        el_.addEventListener('dragenter', this.dragenter, false);
        el_.addEventListener('dragover', this.dragover, false);
        el_.addEventListener('dragleave', this.dragleave, false);
        el_.addEventListener('drop', this.drop, false);
    }
});

/* See license.txt for terms of usage */

/**
 * @module tabs/homeTab
 */
define("tabs/homeTab", [
    "domplate/domplate",
    "domplate/tabView",
    "core/lib",
    "core/trace",
    "i18n!nls/homeTab",
    "text!tabs/homeTab.html",
    "preview/harModel",
    "dnd"
],

function(Domplate, TabView, Lib, Trace, Strings, HomeTabHtml, HarModel, dnd) { with (Domplate) {

var FILE_READ_OPTIONS = {
    type: 'openFile',
    accepts: [
        { extensions: ['har'] }
    ]
};

//*************************************************************************************************
// Home Tab

/**
 * @constructor module:tabs/homeTab
 */
function HomeTab() {}
HomeTab.prototype = Lib.extend(TabView.Tab.prototype,
/** @lends HomeTab.prototype */
{
    id: "Home",
    label: Strings.homeTabLabel,

    bodyTag:
        DIV({"class": "homeBody"}),

    onUpdateBody: function(tabView, body)
    {
        body = this.bodyTag.replace({}, body);

        // Content of this tab is loaded by default (required above) since it's
        // the first thing displayed to the user anyway.
        // Also let's search and replace some constants in the template.
        body.innerHTML = HomeTabHtml.replace("http://www.softwareishard.com/blog/har-12-spec/", tabView.harSpecURL, "g");

        // Register click handlers.
        $("#appendPreview").click(Lib.bindFixed(this.onAppendPreview, this));
        $(".linkAbout").click(Lib.bind(this.onAbout, this));

        var dragOverlay = document.getElementById('file-drop-overlay');

        function dragEnter(event) {
            dragOverlay.style.height = '100%';
        }

        function dragLeave(event) {
            dragOverlay.style.height = null;
        }

        // Registers drag-and-drop event handlers. These will be responsible for
        // auto-loading all dropped HAR files.
        dnd('#home-tab', this.handleDrop.bind(this), dragEnter, dragLeave);
        
        document.getElementById('select-file').addEventListener('change', function (event) {
            var fileProcessed = this.handleDrop(event.target);
            if(fileProcessed) {
                setTimeout(function() {
                    event.target.value = '';
                }, 500);
            }
        }.bind(this));
    },

    // * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
    // Events

    onAppendPreview: function(jsonString)
    {
        if (!jsonString)
            jsonString = $("#sourceEditor").val();

        if (jsonString)
            this.tabView.appendPreview(jsonString);
    },

    onAbout: function()
    {
        this.tabView.selectTabByName("About");
    },

    // * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

    onDrop: function(event)
    {
        var e = Lib.fixEvent(event);
        Lib.cancelEvent(e);

        try
        {
            this.handleDrop(event.originalEvent.dataTransfer);
        }
        catch (err)
        {
            Trace.exception("HomeTab.onDrop EXCEPTION", err);
        }
    },

    handleDrop: function(dataTransfer)
    {
        if (!dataTransfer) {
            return false;
        }

        var files = dataTransfer.files;
        if (!files) {
            return false;
        }

        var file = files[0];
        var ext = Lib.getFileExtension(file.name);
        if (ext.toLowerCase() != "har") {
            return false;
        }

        var self = this;
        var reader = this.getFileReader(file, function(text) {
            if (text) {
                self.onAppendPreview(text);
            }
        });
        reader();
        
        return true;
    },

    /**
     * File reader callback.
     *
     * @callback fileReaderCallback
     * @param {String} contents
     *  file contents
     */

     /**
     * @param {Object} file
     *  The file to get the text for.
     * @param {fileReaderCallback} callback
     *  Callback to receive the file contents.
     */
    getFileReader: function(file, callback)
    {
        return function fileReader()
        {
            if (typeof(file.getAsText) != "undefined")
            {
                callback(file.getAsText(""));
                return;
            }

            if (typeof(FileReader) != "undefined")
            {
                var fileReader = new FileReader();
                fileReader.onloadend = function() {
                    callback(fileReader.result);
                };
                fileReader.readAsText(file);
            }
        }
    },

    // * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

    loadInProgress: function(show, msg)
    {
        $("#sourceEditor").val(show ? (msg ? msg : Strings.loadingHar) : "");
    }
});

return HomeTab;

//*************************************************************************************************
}});

/* See license.txt for terms of usage */

define(
'nls/harViewer',{
    "root": {
        "aboutTabLabel": "About",
        "schemaTabLabel": "Schema"
    }
});



define('text!tabs/aboutTab.html',[],function () { return '<div>\r\n<h2>HTTP Archive Viewer</h2>\r\n<table style="width:600px;">\r\n<tr><td>\r\n<p>The purpose of this online tool is to visualize\r\n<a href="http://www.softwareishard.com/blog/har-12-spec/">\r\n    HTTP Archive (HAR)</a>\r\nlog files created by HTTP tracking tools. These files contain log of HTTP\r\nclient/server conversation and can be used for an additional analysis of e.g. \r\npage load performance.</p>\r\n\r\n<p>User interface of this tool is composed from the following tabs:</p>\r\n<ul>\r\n<li><b>Load</b> - Paste content of a log file into the text box in this tab.</li>\r\n<li><b>Inspect</b> - Switch to this tab if you want to see visualised HTTP traffic.</li>\r\n</ul>\r\n</td></tr>\r\n\r\n<tr><td>\r\n<h3>HTTP Archive Specification</h3>\r\n<p>Required\r\n<a href="http://www.softwareishard.com/blog/har-12-spec/">\r\nstructure</a> of the input HTTP Archive file (*.har) is described using\r\n<a href="http://www.json.com/json-schema-proposal/">JSON Schema</a>.\r\nYou can explore the current schema definition within the <span class="linkSchema link">Schema</span>\r\ntab on this page.</p>\r\n</td></tr>\r\n\r\n<tr><td>\r\n<h3>Request Timing Fields</h3>\r\n<p>Part of the HTTP log is also a timing info about network request executions.\r\nHere is a description of individual request/response phases:</p>\r\n<ul>\r\n<li><i>Blocking</i> - Time spent in a queue waiting for a network connection.</li>\r\n<li><i>DNS Lookup</i> - DNS resolution time. The time required to resolve a host name.</li>\r\n<li><i>Connecting</i> - Time required to create TCP connection.</li>\r\n<li><i>Sending</i> - Time required to send HTTP request to the server.</li>\r\n<li><i>Waiting</i> - Waiting for a response from the server.</li>\r\n<li><i>Receiving</i> - Time required to read entire response from the server (or cache).</li>\r\n</ul>\r\n</td></tr>\r\n\r\n<tr><td>\r\n<h3>Online Log Files</h3>\r\n<p>HAR Viewer also supports JSONP and so, it\'s possible to load log files \r\nfrom different domains. This allows linking your online logs and preview them\r\nautomatically within the viewer. See live\r\n<a href="?inputUrl=http://www.janodvarko.cz/har/viewer/examples/inline-scripts-block.harp">example</a>.\r\n</p>\r\n\r\n<p><i>1. The Content of a *.har file must be enclosed within a callback function:</i></p>\r\n<code>onInputData({ "log": { ... } })</code>\r\n\r\n<p><i>2. The link displaying a *.har file (using this viewer) must specify URL of\r\nthe file in <b>inputUrl</b> parameter:</i></p>\r\n<code>http://www.softwareishard.com/har/viewer/?inputUrl=http://www.example.com/netData.har</code>\r\n\r\n<p><i>3. A custom name of the callback function can be specified in a <b>callback</b> parameter\r\n(by default it\'s <b>onInputData</b>):</i></p>\r\n<code>http://www.softwareishard.com/har/viewer/?inputUrl=http://www.example.com/netData.har&amp;callback=onInputData</code>\r\n<br/><br/>\r\n</td></tr>\r\n\r\n</table>\r\n\r\n<br/><br/>\r\n<i>HAR Viewer author: Jan Odvarko, <EMAIL></i>\r\n</div>\r\n';});

/* See license.txt for terms of usage */

/**
 * @module tabs/aboutTab
 */
define("tabs/aboutTab", [
    "domplate/domplate",
    "domplate/tabView",
    "core/lib",
    "i18n!nls/harViewer",
    "text!tabs/aboutTab.html"
],

function(Domplate, TabView, Lib, Strings, AboutTabHtml) { with (Domplate) {

//*************************************************************************************************
// Home Tab

/**
 * @constructor module:tabs/aboutTab
 */
function AboutTab() {}
AboutTab.prototype =
{
    id: "About",
    label: Strings.aboutTabLabel,

    tabHeaderTag:
        A({"class": "$tab.id\\Tab tab", view: "$tab.id", _repObject: "$tab"},
            "$tab.label"
        ),

    bodyTag:
        DIV({"class": "aboutBody"}),

    onUpdateBody: function(tabView, body)
    {
        var self = this;
        body = this.bodyTag.replace({}, body);
        body.innerHTML = AboutTabHtml;
        $(".linkSchema").click(Lib.bind(self.onSchema, self));
    },

    onSchema: function()
    {
        this.tabView.selectTabByName("Schema");
    }
};

return AboutTab;

//*************************************************************************************************
}});

/* See license.txt for terms of usage */

define(
'nls/previewTab',{
    "root": {
        "previewTabLabel": "Inspect",
        "showTimelineButton": "Show Page Timeline",
        "hideTimelineButton": "Hide Page Timeline",
        "printPageButton": "Print Page",
        "showTimelineTooltip": "Show/hide statistic preview for selected pages in the timeline.",
        "showStatsButton": "Show Statistics",
        "hideStatsButton": "Hide Statistics",
        "showStatsTooltip": "Show/hide page timeline.",
        "clearButton": "Clear",
        "clearTooltip": "Remove all HAR logs from the viewer",
        "downloadTooltip": "Download all current data in one HAR file.",
        "downloadError": "Failed to save HAR data",
        "menuShowHARSource": "Show HAR Source"
    }
});


/* See license.txt for terms of usage */

/**
 * @module domplate/popupMenu
 */
define("domplate/popupMenu", [
    "domplate/domplate",
    "core/lib",
    "core/trace"
],

function(Domplate, Lib, Trace) { with (Domplate) {

// ************************************************************************************************
// Controller

var Controller =
{
    controllers: [],
    controllerContext: {label: "controller context"},

    initialize: function(context)
    {
        this.controllers = [];
        this.controllerContext = context || this.controllerContext;
    },

    shutdown: function()
    {
        this.removeControllers();
    },

    addController: function()
    {
        for (var i=0, arg; arg=arguments[i]; i++)
        {
            // If the first argument is a string, make a selector query 
            // within the controller node context
            if (typeof arg[0] == "string")
            {
                arg[0] = $$(arg[0], this.controllerContext);
            }

            // bind the handler to the proper context
            var handler = arg[2];
            arg[2] = Lib.bind(handler, this);
            // save the original handler as an extra-argument, so we can
            // look for it later, when removing a particular controller
            arg[3] = handler;

            this.controllers.push(arg);
            Lib.addEventListener.apply(this, arg);
        }
    },

    removeController: function()
    {
        for (var i=0, arg; arg=arguments[i]; i++)
        {
            for (var j=0, c; c=this.controllers[j]; j++)
            {
                if (arg[0] == c[0] && arg[1] == c[1] && arg[2] == c[3])
                    Lib.removeEventListener.apply(this, c);
            }
        }
    },

    removeControllers: function()
    {
        for (var i=0, c; c=this.controllers[i]; i++)
        {
            Lib.removeEventListener.apply(this, c);
        }
    }
};

//***********************************************************************************************//
// Menu

var menuItemProps = {
    "class": "$item.className",
    type: "$item.type",
    value: "$item.value",
    _command: "$item.command"
};

if (Lib.isIE6)
    menuItemProps.href = "javascript:void(0)";

var MenuPlate = domplate(
{
    tag:
        DIV({"class": "popupMenu popupMenuShadow"},
            DIV({"class": "popupMenuContent popupMenuShadowContent"},
                FOR("item", "$object.items|memberIterator",
                    TAG("$item.tag", {item: "$item"})
                )
            )
        ),

    itemTag:
        A(menuItemProps,
            "$item.label"
        ),

    checkBoxTag:
        A(Lib.extend(menuItemProps, {checked : "$item.checked"}),
            "$item.label"
        ),

    radioButtonTag:
        A(Lib.extend(menuItemProps, {selected : "$item.selected"}),
            "$item.label"
        ),

    groupTag:
        A(Lib.extend(menuItemProps, {child: "$item.child"}),
            "$item.label"
        ),

    shortcutTag:
        A(menuItemProps,
            "$item.label",
            SPAN({"class": "popupMenuShortcutKey"},
                "$item.key"
            )
        ),

    separatorTag:
        SPAN({"class": "popupMenuSeparator"}),

    memberIterator: function(items)
    {
        var result = [];
        for (var i=0, length=items.length; i<length; i++)
        {
            var item = items[i];

            // separator representation
            if (typeof item == "string" && item.indexOf("-") == 0)
            {
                result.push({tag: this.separatorTag});
                continue;
            }

            item = Lib.extend(item, {});
            item.type = item.type || "";
            item.value = item.value || "";

            var type = item.type;

            // default item representation
            item.tag = this.itemTag;

            var className = item.className || "";
            className += "popupMenuOption ";

            // specific representations
            if (type == "checkbox")
            {
                className += "popupMenuCheckBox ";
                item.tag = this.checkBoxTag;
            }
            else if (type == "radio")
            {
                className += "popupMenuRadioButton ";
                item.tag = this.radioButtonTag;
            }
            else if (type == "group")
            {
                className += "popupMenuGroup ";
                item.tag = this.groupTag;
            }
            else if (type == "shortcut")
            {
                className += "popupMenuShortcut ";
                item.tag = this.shortcutTag;
            }

            if (item.checked)
                className += "popupMenuChecked ";
            else if (item.selected)
                className += "popupMenuRadioSelected ";

            if (item.disabled)
                className += "popupMenuDisabled ";

            item.className = className;
            item.label = item.label;
            result.push(item);
        }

        return result;
    }
});

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

function Menu(options)
{
    // if element is not pre-rendered, we must render it now
    if (!options.element)
    {
        if (options.getItems)
            options.items = options.getItems();

        // Trim separators
        if (options.items[0] == "-")
            options.items.shift();
        if (options.items[options.items.length - 1] == "-")
            options.items.pop();

        var body = Lib.getBody(document);
        options.element = MenuPlate.tag.append({object: options}, body, MenuPlate);
    }

    // extend itself with the provided options
    Lib.append(this, options);

    if (typeof this.element == "string")
    {
        this.id = this.element;
        this.element = $(this.id);
    }
    else if (this.id)
    {
        this.element.id = this.id;
    }

    this.elementStyle = this.element.style;
    this.isVisible = false;

    this.handleMouseDown = Lib.bind(this.handleMouseDown, this);
    this.handleMouseOver = Lib.bind(this.handleMouseOver, this);
    this.handleMouseOut = Lib.bind(this.handleMouseOut, this);
    this.handleWindowMouseDown = Lib.bind(this.handleWindowMouseDown, this);
};

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

var menuMap = {};

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

Menu.prototype = Lib.extend(Controller,
{
    initialize: function()
    {
        Controller.initialize.call(this);

        this.addController(
            [this.element, "mousedown", this.handleMouseDown],
            [this.element, "mouseover", this.handleMouseOver]
        );
    },

    destroy: function()
    {
        this.hide();

        // if it is a childMenu, remove its reference from the parentMenu
        if (this.parentMenu)
            this.parentMenu.childMenu = null;

        // remove the element from the document
        this.element.parentNode.removeChild(this.element);

        // clear references
        this.element = null;
        this.elementStyle = null;
        this.parentMenu = null;
        this.parentTarget = null;
    },

    shutdown: function()
    {
        Controller.shutdown.call(this);
    },

    showPopup: function(target)
    {
        var offsetLeft = Lib.isIE6 ? 1 : -4;  // IE6 problem with fixed position
        var box = Lib.getElementBox(target);
        var offset = {top: 0, left: 0};

        this.show(
            box.left + offsetLeft - offset.left,
            box.top + box.height - 5 - offset.top
        );
    },

    show: function(x, y)
    {
        this.initialize();

        if (this.isVisible)
            return;

        x = x || 0;
        y = y || 0;

        if (this.parentMenu)
        {
            var oldChildMenu = this.parentMenu.childMenu;
            if (oldChildMenu && oldChildMenu != this)
            {
                oldChildMenu.destroy();
            }

            this.parentMenu.childMenu = this;
        }
        else
        {
            Lib.addEventListener(document, "mousedown", this.handleWindowMouseDown);
        }

        this.elementStyle.display = "block";
        this.elementStyle.visibility = "hidden";

        var size = Lib.getWindowSize();

        x = Math.min(x, size.width - this.element.clientWidth - 10);
        x = Math.max(x, 0);

        y = Math.min(y, size.height - this.element.clientHeight - 10);
        y = Math.max(y, 0);

        this.elementStyle.left = x + "px";
        this.elementStyle.top = y + "px";
        this.elementStyle.visibility = "visible";
        this.isVisible = true;

        if (Lib.isFunction(this.onShow))
            this.onShow.apply(this, arguments);
    },

    hide: function()
    {
        this.clearHideTimeout();
        this.clearShowChildTimeout();

        if (!this.isVisible)
            return;

        this.elementStyle.display = "none";

        if (this.childMenu)
        {
            this.childMenu.destroy();
            this.childMenu = null;
        }

        if (this.parentTarget)
            Lib.removeClass(this.parentTarget, "popupMenuGroupSelected");

        this.isVisible = false;
        this.shutdown();

        if (Lib.isFunction(this.onHide))
            this.onHide.apply(this, arguments);
    },

    showChildMenu: function(target)
    {
        var id = target.getAttribute("child");
        var parent = this;
        var target = target;

        this.showChildTimeout = window.setTimeout(function()
        {
            //if (!parent.isVisible) return;
            var box = Lib.getElementBox(target);
            var childMenuObject = menuMap.hasOwnProperty(id) ? menuMap[id] : {element: $(id)};

            var childMenu = new Menu(Lib.extend(childMenuObject,
            {
                parentMenu: parent,
                parentTarget: target
            }));

            var offsetLeft = Lib.isIE6 ? -1 : -6; // IE6 problem with fixed position
            childMenu.show(box.left + box.width + offsetLeft, box.top -6);
            Lib.setClass(target, "popupMenuGroupSelected");
        },350);
    },

    clearHideTimeout: function()
    {
        if (this.hideTimeout)
        {
            window.clearTimeout(this.hideTimeout);
            delete this.hideTimeout;
        }
    },

    clearShowChildTimeout: function()
    {
        if(this.showChildTimeout)
        {
            window.clearTimeout(this.showChildTimeout);
            this.showChildTimeout = null;
        }
    },

    handleMouseDown: function(event)
    {
        Lib.cancelEvent(event, true);

        var topParent = this;
        while (topParent.parentMenu)
            topParent = topParent.parentMenu;

        var target = event.target || event.srcElement;

        target = Lib.getAncestorByClass(target, "popupMenuOption");

        if(!target || Lib.hasClass(target, "popupMenuGroup"))
            return false;

        if (target && !Lib.hasClass(target, "popupMenuDisabled"))
        {
            var type = target.getAttribute("type");
            
            if (type == "checkbox")
            {
                var checked = target.getAttribute("checked");
                var value = target.getAttribute("value");
                var wasChecked = Lib.hasClass(target, "popupMenuChecked");

                if (wasChecked)
                {
                    Lib.removeClass(target, "popupMenuChecked");
                    target.setAttribute("checked", "");
                }
                else
                {
                    Lib.setClass(target, "popupMenuChecked");
                    target.setAttribute("checked", "true");
                }

                if (Lib.isFunction(this.onCheck))
                    this.onCheck.call(this, target, value, !wasChecked)
            }

            if (type == "radiobutton")
            {
                var selectedRadios = Lib.getElementsByClass(target.parentNode, "popupMenuRadioSelected");
                var group = target.getAttribute("group");

                for (var i = 0, length = selectedRadios.length; i < length; i++)
                {
                    radio = selectedRadios[i];

                    if (radio.getAttribute("group") == group)
                    {
                        Lib.removeClass(radio, "popupMenuRadioSelected");
                        radio.setAttribute("selected", "");
                    }
                }

                Lib.setClass(target, "popupMenuRadioSelected");
                target.setAttribute("selected", "true");
            }

            var handler = null;

            // target.command can be a function or a string. 
            var cmd = target.command;

            // If it is a function it will be used as the handler
            // If it is a string, tha handler is the property of the current menu object 
            // will be used as the handler
            if (Lib.isFunction(cmd))
                handler = cmd;
            else if (typeof cmd == "string")
                handler = this[cmd];

            var closeMenu = true;
            if (handler)
                closeMenu = handler.call(this, target) !== false;

            if (closeMenu)
                topParent.hide();
        }

        return false;
    },

    handleWindowMouseDown: function(event)
    {
        var target = event.target || event.srcElement;
        target = Lib.getAncestorByClass(target, "popupMenu");
        if (!target)
        {
            Lib.removeEventListener(document, "mousedown", this.handleWindowMouseDown);
            this.destroy();
        }
    },

    handleMouseOver: function(event)
    {
        this.clearHideTimeout();
        this.clearShowChildTimeout();

        var target = event.target || event.srcElement;

        target = Lib.getAncestorByClass(target, "popupMenuOption");

        if (!target)
            return;

        var childMenu = this.childMenu;
        if (childMenu) 
        {
            Lib.removeClass(childMenu.parentTarget, "popupMenuGroupSelected");
            
            if (childMenu.parentTarget != target && childMenu.isVisible)
            {
                childMenu.clearHideTimeout(); 
                childMenu.hideTimeout = window.setTimeout(function(){
                    childMenu.destroy();
                },300);
            }
        }

        if (Lib.hasClass(target, "popupMenuGroup"))
        {
            this.showChildMenu(target);
        }
    }
});

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

Lib.append(Menu,
{
    register: function(object)
    {
        menuMap[object.id] = object;
    },

    check: function(element)
    {
        Lib.setClass(element, "popupMenuChecked");
        element.setAttribute("checked", "true");
    },

    uncheck: function(element)
    {
        Lib.removeClass(element, "popupMenuChecked");
        element.setAttribute("checked", "");
    },

    disable: function(element)
    {
        Lib.setClass(element, "popupMenuDisabled");
    },

    enable: function(element)
    {
        Lib.removeClass(element, "popupMenuDisabled");
    }
});

// **********************************************************************************************//

return Menu;

// **********************************************************************************************//
}});

/* See license.txt for terms of usage */

/**
 * @module domplate/toolbar
 */
define("domplate/toolbar", [
    "domplate/domplate",
    "core/lib",
    "core/trace",
    "domplate/popupMenu"
],

function(Domplate, Lib, Trace, Menu) { with (Domplate) {

//*************************************************************************************************

/**
 * @domplate Represents a toolbar widget.
 */
var ToolbarTempl = domplate(
/** @lends ToolbarTempl */
{
    tag:
        DIV({"class": "toolbar", onclick: "$onClick"}),

    buttonTag:
        SPAN({"class": "$button|getClassName toolbarButton", title: "$button.tooltiptext",
            $text: "$button|hasLabel", onclick: "$button|getCommand"},
            "$button|getLabel"
        ),

    dropDownTag:
        SPAN({"class": "$button|getClassName toolbarButton dropDown",
            _repObject: "$button",
            title: "$button.tooltiptext",
            $text: "$button|hasLabel", onclick: "$onDropDown"},
            "$button|getLabel",
            SPAN({"class": "arrow"})
        ),

    separatorTag:
        SPAN({"class": "toolbarSeparator", style: "color: gray;"}, "|"),

    hasLabel: function(button)
    {
        return button.label ? true : false;
    },

    getLabel: function(button)
    {
        return button.label ? button.label : "";
    },

    getClassName: function(button)
    {
        return button.className ? button.className : "";
    },

    getCommand: function(button)
    {
        return button.command ? button.command : function() {};
    },

    onClick: function(event)
    {
        var e = $.event.fix(event || window.event);

        // Cancel button clicks so they are not propagated further.
        Lib.cancelEvent(e);
    },

    onDropDown: function(event)
    {
        var e = $.event.fix(event || window.event);

        var target = e.target;
        var button = Lib.getAncestorByClass(target, "toolbarButton");
        var items = button.repObject.items;

        var menu = new Menu({id: "toolbarContextMenu", items: items});
        menu.showPopup(button);
    }
});

// ********************************************************************************************* //

/**
 * Toolbat widget.
 */
function Toolbar()
{
    this.buttons = [];
}

Toolbar.prototype =
/** @lends Toolbar */
{
    addButton: function(button)
    {
        if (!button.tooltiptext)
            tooltiptext = "";
        this.buttons.push(button);
    },

    removeButton: function(buttonId)
    {
        for (var i=0; i<this.buttons.length; i++)
        {
            if (this.buttons[i].id == buttonId)
            {
                this.buttons.splice(i, 1);
                break;
            }
        }
    },

    addButtons: function(buttons)
    {
        for (var i=0; i<buttons.length; i++)
            this.addButton(buttons[i]);
    },

    getButton: function(buttonId)
    {
        for (var i=0; i<this.buttons.length; i++)
        {
            if (this.buttons[i].id == buttonId)
                return this.buttons[i];
        }
    },

    render: function(parentNode)
    {
        // Don't render if there are no buttons. Note that buttons can be removed
        // as part of viewer customization.
        if (!this.buttons.length)
            return;

        // Use the same parent as before if just re-rendering.
        if (this.element)
            parentNode = this.element.parentNode;

        this.element = ToolbarTempl.tag.replace({}, parentNode);
        for (var i=0; i<this.buttons.length; i++)
        {
            var button = this.buttons[i];
            var defaultTag = button.items ? ToolbarTempl.dropDownTag : ToolbarTempl.buttonTag;
            var tag = button.tag ? button.tag : defaultTag;

            var element = tag.append({button: button}, this.element);

            if (button.initialize)
                button.initialize(element);

            if (i<this.buttons.length-1)
                ToolbarTempl.separatorTag.append({}, this.element);
        }

        return this.element;
    }
};

return Toolbar;

// ************************************************************************************************
}});

/* See license.txt for terms of usage */

define(
'nls/pageTimeline',{
    "root": {
        "pageLoad": "Page Load",
        "request": "Request",
        "requests": "Requests",
        "pageBarTooltip": "Click to select and include in statistics preview."
    }
});


/* See license.txt for terms of usage */

/**
 * @module tabs/pageTimeline
 */
define("tabs/pageTimeline", [
    "domplate/domplate",
    "core/lib",
    "core/trace",
    "i18n!nls/pageTimeline",
    "preview/harModel"
],

function(Domplate, Lib, Trace, Strings, HarModel) { with (Domplate) {

//*************************************************************************************************
// Timeline

/**
 * Represents a list of pages displayed as a list of vertical graphs. this object
 * is implemented as a template so, it can render itself.
 */
function Timeline()
{
    this.listeners = [];
    this.element = null;
    this.maxElapsedTime = -1;

    // List of all selected bars.
    this.lastSelectedBar = null;
}

Timeline.prototype = domplate(
{
    graphCols:
        FOR("page", "$input|getPages",
            TD({"class": "pageTimelineCol"},
                DIV({"class": "pageBar", _input: "$input", _page: "$page",
                    title: Strings.pageBarTooltip,
                    style: "height: $page|getHeight\\px",
                    onmousemove: "$onMouseMove",
                    onclick: "$onClick"})
            )
        ),

    pageGraph:
        TABLE({"class": "pageTimelineTable", cellpadding: 0, cellspacing: 0},
            TBODY(
                TR({"class": "pageTimelineRow"},
                    TAG("$graphCols", {input: "$input"})
                )
            )
        ),

    tag:
        DIV({"class": "pageTimelineBody", style: "height: auto; display: none"},
            TABLE({style: "margin: 7px;", cellpadding: 0, cellspacing: 0},
                TBODY(
                    TR(
                        TD(
                            TAG("$pageGraph", {input: "$input"})
                        )
                    ),
                    TR(
                        TD({"class": "pageDescContainer", colspan: 2})
                    )
                )
            )
        ),

    // * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

    getHeight: function(page)
    {
        var height = 1;
        var onLoad = page.pageTimings.onLoad;
        if (onLoad > 0 && this.maxElapsedTime > 0)
            height = Math.round((onLoad / this.maxElapsedTime) * 100);

        return Math.max(1, height);
    },

    onClick: function(event)
    {
        var e = Lib.fixEvent(event);

        var bar = e.target;
        if (!Lib.hasClass(bar, "pageBar"))
            return;

        var control = Lib.isControlClick(e);
        var shift = Lib.isShiftClick(e);

        var row = Lib.getAncestorByClass(bar, "pageTimelineRow");

        // If no modifier is active remove the current selection.
        if (!control && !shift)
            Selection.unselectAll(row, bar);

        // Clicked bar toggles its selection state
        Selection.toggle(bar);

        this.selectionChanged();
    },

    onMouseMove: function(event)
    {
        var e = Lib.fixEvent(event);

        // If the mouse moves over a bar, update a description displayed below and
        // notify all registered listeners.
        var bar = e.target;
        if (!Lib.hasClass(bar, "pageBar"))
            return;

        if (this.highlightedPage == bar.page)
            return;

        this.highlightedPage = bar.page;

        var parentNode = Lib.getElementByClass(this.element, "pageDescContainer");
        Timeline.Desc.render(parentNode, bar);
    },

    // * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

    getPages: function(input)
    {
        return input.log.pages ? input.log.pages : [];
    },

    getPageBar: function(page)
    {
        if (!this.element)
            return;

        // Iterate over all columns and find the one that represents the page.
        var table = Lib.getElementByClass(this.element, "pageTimelineTable");
        var col = table.firstChild.firstChild.firstChild;
        while (col)
        {
            if (col.firstChild.page == page)
                return col.firstChild;
            col = col.nextSibling;
        }
    },

    recalcLayout: function()
    {
        this.maxElapsedTime = 0;
        var prevMaxElapsedTime = this.maxElapsedTime;

        // Iterate over all pages and find the max load-time so, the vertical
        // graph extent can be set.
        var bars = Lib.getElementsByClass(this.element, "pageBar");
        for (var i=0; i<bars.length; i++)
        {
            var page = bars[i].page;
            var onLoadTime = page.pageTimings.onLoad;
            if (onLoadTime > 0 && this.maxElapsedTime < onLoadTime)
                this.maxElapsedTime = onLoadTime;
        }

        // Recalculate height of all pages only if there is a new maximum.
        if (prevMaxElapsedTime != this.maxElapsedTime)
        {
            for (var i=0; i<bars.length; i++)
                bars[i].style.height = this.getHeight(bars[i].page) + "px";
        }
    },

    updateDesc: function()
    {
        if (!this.isVisible())
            return;

        // Make sure the description (tooltip) is displayed for the first
        // page automatically.
        if (!this.highlightedPage)
        {
            var firstBar = Lib.getElementByClass(this.element, "pageBar");
            if (firstBar)
                Lib.fireEvent(firstBar, "mousemove");
        }
    },

    // * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
    // Listeners

    addListener: function(listener)
    {
        this.listeners.push(listener);
    },

    removeListener: function(listener)
    {
        remove(this.listeners, listener);
    },

    // * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
    // Selection

    selectionChanged: function()
    {
        // Notify listeners such as the statistics preview
        var pages = this.getSelection();
        Lib.dispatch(this.listeners, "onSelectionChange", [pages]);
    },

    removeSelection: function()
    {
        if (!this.element)
            return;

        var row = Lib.getElementByClass(this.element, "pageTimelineRow");
        Selection.unselectAll(row);

        this.selectionChanged();
    },

    getSelection: function()
    {
        if (!this.isVisible())
            return [];

        var row = Lib.getElementByClass(this.element, "pageTimelineRow");
        return Selection.getSelection(row);
    },

    // * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
    // Public

    show: function(animation)
    {
        if (this.isVisible())
            return;

        if (!animation)
            this.element.style.display = "block";
        else
            $(this.element).slideDown();

        Lib.setClass(this.element, "opened");
        this.updateDesc();
    },

    hide: function(animation)
    {
        if (!this.isVisible())
            return;

        if (!animation)
            this.element.style.display = "none";
        else
            $(this.element).slideUp();

        Lib.removeClass(this.element, "opened");

        // Remove all selecteed page and so, the stats can update.
        this.removeSelection();
    },

    isVisible: function()
    {
        return Lib.hasClass(this.element, "opened");
    },

    toggle: function(animation)
    {
        if (this.isVisible())
            this.hide(animation);
        else
            this.show(animation);
    },

    render: function(parentNode)
    {
        // Render basic structure. Some pages could be rendered now, but let's
        // do it in the append method.
        this.element = this.tag.replace({input: {log: {pages: []}}}, parentNode, this);
        this.recalcLayout();
        return this.element;
    },

    append: function(input, parentNode)
    {
        // If it's not rendered yet, bail out.
        if (!this.element)
            return;

        // Otherwise just append a new columns to the existing graph.
        var timelineRow = Lib.getElementByClass(this.element, "pageTimelineRow");
        this.graphCols.insertCols({input: input}, timelineRow, this);

        this.recalcLayout();
        this.updateDesc();
    }
});

//*************************************************************************************************
// Timeline Description

Timeline.Desc = domplate(
{
    tag:
        DIV({"class": "pageDescBox"},
            DIV({"class": "connector"}),
            DIV({"class": "desc"},
                SPAN({"class": "summary"}, "$object|getSummary"),
                SPAN({"class": "time"}, "$object.page|getTime"),
                SPAN({"class": "title"}, "$object.page|getTitle"),
                PRE({"class": "comment"}, "$object.page|getComment")
            )
        ),

    getSummary: function(object)
    {
        var summary = "";
        var onLoad = object.page.pageTimings.onLoad;
        if (onLoad > 0)
            summary += Strings.pageLoad + ": " + Lib.formatTime(onLoad) + ", ";

        var requests = HarModel.getPageEntries(object.input, object.page);
        var count = requests.length;
        summary += count + " " + (count == 1 ? Strings.request : Strings.requests);

        return summary;
    },

    getTime: function(page)
    {
        var pageStart = Lib.parseISO8601(page.startedDateTime);
        var date = new Date(pageStart);
        return date.toLocaleString();
    },

    getTitle: function(page)
    {
        return page.title;
    },

    getComment: function(page)
    {
        return page.comment ? page.comment : "";
    },

    render: function(parentNode, bar)
    {
        var object = {
            input: bar.input,
            page: bar.page
        };

        var element = this.tag.replace({object: object}, parentNode);
        var conn = Lib.$(element, "connector");
        conn.style.marginLeft = bar.parentNode.offsetLeft + "px";
        return element;
    }
});

//*************************************************************************************************

var Selection =
{
    isSelected: function(bar)
    {
        return Lib.hasClass(bar, "selected");
    },

    toggle: function(bar)
    {
        Lib.toggleClass(bar, "selected");
    },

    select: function(bar)
    {
        if (!this.isSelected(bar))
            Lib.setClass(bar, "selected");
    },

    unselect: function(bar)
    {
        if (this.isSelected(bar))
            Lib.removeClass(bar, "selected");
    },

    getSelection: function(row)
    {
        var pages = [];
        var bars = Lib.getElementsByClass(row, "pageBar");
        for (var i=0; i<bars.length; i++)
        {
            var bar = bars[i];
            if (this.isSelected(bar))
                pages.push(bar.page);
        }
        return pages;
    },

    unselectAll: function(row, except)
    {
        var bars = Lib.getElementsByClass(row, "pageBar");
        for (var i=0; i<bars.length; i++)
        {
            if (except != bars[i])
                this.unselect(bars[i]);
        }
    }
}

//*************************************************************************************************

return Timeline;

//*************************************************************************************************
}});

/* See license.txt for terms of usage */

define(
'nls/pageStats',{
    "root": {
        "pieLabelDNS": "DNS",
        "pieLabelSSL": "SSL/TLS",
        "pieLabelConnect": "Connect",
        "pieLabelBlocked": "Blocked",
        "pieLabelSend": "Send",
        "pieLabelWait": "Wait",
        "pieLabelReceive": "Receive",

        "pieLabelHTMLText": "HTML/Text",
        "pieLabelJavaScript": "JavaScript",
        "pieLabelCSS": "CSS",
        "pieLabelImage": "Image",
        "pieLabelFlash": "Flash",
        "pieLabelOthers": "Others",

        "pieLabelHeadersSent": "Headers Sent",
        "pieLabelBodiesSent": "Bodies Sent",
        "pieLabelHeadersReceived": "Headers Received",
        "pieLabelBodiesReceived": "Bodies Received",

        "pieLabelDownloaded": "Downloaded",
        "pieLabelPartial": "Partial",
        "pieLabelFromCache": "From Cache"
    }
});


/* See license.txt for terms of usage */

/**
 * @module domplate/infoTip
 */
define("domplate/infoTip", [
    "domplate/domplate",
    "core/lib",
    "core/trace"
],

function(Domplate, Lib, Trace) { with (Domplate) {

//***********************************************************************************************//

var InfoTip = Lib.extend(
{
    listeners: [],
    maxWidth: 100,
    maxHeight: 80,
    infoTipMargin: 10,
    infoTipWindowPadding: 25,

    tags: domplate(
    {
        infoTipTag: DIV({"class": "infoTip"})
    }),

    initialize: function()
    {
        var body = $("body");
        body.bind("mouseover", Lib.bind(this.onMouseMove, this));
        body.bind("mouseout", Lib.bind(this.onMouseOut, this));
        body.bind("mousemove", Lib.bind(this.onMouseMove, this));

        return this.infoTip = this.tags.infoTipTag.append({}, Lib.getBody(document));
    },

    showInfoTip: function(infoTip, target, x, y, rangeParent, rangeOffset)
    {
        var scrollParent = Lib.getOverflowParent(target);
        var scrollX = x + (scrollParent ? scrollParent.scrollLeft : 0);

        // Distribute event to all registered listeners and show the info tip if
        // any of them return true.
        var result = Lib.dispatch2(this.listeners, "showInfoTip",
            [infoTip, target, scrollX, y, rangeParent, rangeOffset])

        if (result)
        {
            var htmlElt = infoTip.ownerDocument.documentElement;
            var panelWidth = htmlElt.clientWidth;
            var panelHeight = htmlElt.clientHeight;

            if (x+infoTip.offsetWidth + this.infoTipMargin >
                panelWidth - this.infoTipWindowPadding)
            {
                infoTip.style.left = "auto";
                infoTip.style.right = ((panelWidth-x) + this.infoTipMargin) + "px";
            }
            else
            {
                infoTip.style.left = (x + this.infoTipMargin) + "px";
                infoTip.style.right = "auto";
            }

            if (y + infoTip.offsetHeight + this.infoTipMargin > panelHeight)
            {
                infoTip.style.top = Math.max(0,
                    panelHeight - (infoTip.offsetHeight + this.infoTipMargin)) + "px";
                infoTip.style.bottom = "auto";
            }
            else
            {
                infoTip.style.top = (y + this.infoTipMargin) + "px";
                infoTip.style.bottom = "auto";
            }

            infoTip.setAttribute("active", "true");
        }
        else
        {
            this.hideInfoTip(infoTip);
        }
    },

    hideInfoTip: function(infoTip)
    {
        if (infoTip)
            infoTip.removeAttribute("active");
    },

    onMouseOut: function(event)
    {
        if (!event.relatedTarget)
            this.hideInfoTip(this.infoTip);
    },

    onMouseMove: function(event)
    {
        // There is no background image for mulitline tooltips.
        this.infoTip.setAttribute("multiline", false);

        var x = event.clientX, y = event.clientY;
        this.showInfoTip(this.infoTip, event.target, x, y, event.rangeParent, event.rangeOffset);
    },

    populateTimingInfoTip: function(infoTip, color)
    {
        this.tags.colorTag.replace({rgbValue: color}, infoTip);
        return true;
    },

    // * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
    // Listeners

    addListener: function(listener)
    {
        this.listeners.push(listener);
    },

    removeListener: function(listener)
    {
        Lib.remove(this.listeners, listener);
    }
});

InfoTip.initialize();

// **********************************************************************************************//

return InfoTip;

// **********************************************************************************************//
}});

/* See license.txt for terms of usage */

/**
 * @module tabs/pageStats
 */
define("tabs/pageStats", [
    "domplate/domplate",
    "core/lib",
    "i18n!nls/pageStats",
    "preview/harSchema",
    "preview/harModel",
    "core/cookies",
    "domplate/infoTip",
    "core/trace"
],

function(Domplate, Lib, Strings, HarSchema, HarModel, Cookies, InfoTip, Trace) { with (Domplate) {

//*************************************************************************************************
// Page Load Statistics

function PieBase() {}
PieBase.prototype =
{
    data: [],
    title: "",

    getLabelTooltipText: function(item)
    {
        return item.label + ": " + Lib.formatSize(item.value);
    },

    cleanUp: function()
    {
        for (var i=0; i<this.data.length; i++)
        {
            this.data[i].value = 0;
            this.data[i].count = 0;
        }
    }
}

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

function TimingPie() {};
TimingPie.prototype = Lib.extend(PieBase.prototype,
{
    title: "Summary of request times.",

    data: [
        {value: 0, label: Strings.pieLabelBlocked, color: "rgb(228, 214, 193)"},
        {value: 0, label: Strings.pieLabelDNS,     color: "rgb(119, 192, 203)"},
        {value: 0, label: Strings.pieLabelSSL,     color: "rgb(168, 196, 173)"},
        {value: 0, label: Strings.pieLabelConnect, color: "rgb(179, 222, 93)"},
        {value: 0, label: Strings.pieLabelSend,    color: "rgb(224, 171, 157)"},
        {value: 0, label: Strings.pieLabelWait,    color: "rgb(163, 150, 190)"},
        {value: 0, label: Strings.pieLabelReceive, color: "rgb(194, 194, 194)"}
    ],

    getLabelTooltipText: function(item)
    {
        return item.label + ": " + Lib.formatTime(item.value);
    }
});

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

function ContentPie() {};
ContentPie.prototype = Lib.extend(PieBase.prototype,
{
    title: "Summary of content types.",

    data: [
        {value: 0, label: Strings.pieLabelHTMLText, color: "rgb(174, 234, 218)"},
        {value: 0, label: Strings.pieLabelJavaScript, color: "rgb(245, 230, 186)"},
        {value: 0, label: Strings.pieLabelCSS, color: "rgb(212, 204, 219)"},
        {value: 0, label: Strings.pieLabelImage, color: "rgb(220, 171, 181)"},
        {value: 0, label: Strings.pieLabelFlash, color: "rgb(166, 156, 222)"},
        {value: 0, label: Strings.pieLabelOthers, color: "rgb(229, 171, 255)"}
    ],

    getLabelTooltipText: function(item)
    {
        return item.count + "x" + " " + item.label + ": " + Lib.formatSize(item.value);
    }
});

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

function TrafficPie() {};
TrafficPie.prototype = Lib.extend(PieBase.prototype,
{
    title: "Summary of sent and received bodies & headers.",

    data: [
        {value: 0, label: Strings.pieLabelHeadersSent, color: "rgb(247, 179, 227)"},
        {value: 0, label: Strings.pieLabelBodiesSent, color: "rgb(226, 160, 241)"},
        {value: 0, label: Strings.pieLabelHeadersReceived, color: "rgb(166, 232, 166)"},
        {value: 0, label: Strings.pieLabelBodiesReceived, color: "rgb(168, 196, 173)"}
    ]
});

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

function CachePie() {};
CachePie.prototype = Lib.extend(PieBase.prototype,
{
    title: "Comparison of downloaded data from the server and browser cache.",

    data: [
        {value: 0, label: Strings.pieLabelDownloaded, color: "rgb(182, 182, 182)"},
        {value: 0, label: Strings.pieLabelPartial, color: "rgb(218, 218, 218)"},
        {value: 0, label: Strings.pieLabelFromCache, color: "rgb(239, 239, 239)"}
    ],

    getLabelTooltipText: function(item)
    {
        return item.count + "x" + " " + item.label + ": " + Lib.formatSize(item.value);
    }
});

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

var timingPie = new TimingPie();
var contentPie = new ContentPie();
var trafficPie = new TrafficPie();
var cachePie = new CachePie();

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

var jsTypes = {
    "text/javascript": 1,
    "text/jscript": 1,
    "application/javascript": 1,
    "application/x-javascript": 1,
    "text/js": 1
}

var htmlTypes = {
    "text/plain": 1,
    "text/html": 1
}

var cssTypes = {
    "text/css": 1
}

var imageTypes = {
    "image/png": 1,
    "image/jpeg": 1,
    "image/gif": 1
}

var flashTypes = {
    "application/x-shockwave-flash": 1
}

var jsonTypes = {
    "text/x-json": 1,
    "text/x-js": 1,
    "application/json": 1,
    "application/x-js": 1
}

var xmlTypes = {
    "application/xml": 1,
    "application/xhtml+xml": 1,
    "application/vnd.mozilla.xul+xml": 1,
    "text/xml": 1,
    "text/xul": 1,
    "application/rdf+xml": 1
}

var unknownTypes = {
    "text/xsl": 1,
    "text/sgml": 1,
    "text/rtf": 1,
    "text/x-setext": 1,
    "text/richtext": 1,
    "text/tab-separated-values": 1,
    "text/rdf": 1,
    "text/xif": 1,
    "text/ecmascript": 1,
    "text/vnd.curl": 1,
    "text/vbscript": 1,
    "view-source": 1,
    "view-fragment": 1,
    "application/x-httpd-php": 1,
    "application/ecmascript": 1,
    "application/http-index-format": 1
};

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

function Stats(model, timeline)
{
    this.model = model;
    this.timeline = timeline;
    this.timeline.addListener(this);
}

/**
 * @domplate Template for statistics section (pie graphs)
 */
Stats.prototype = domplate(
/** @lends Stats */
{
    element: null,

    tag:
        DIV({"class": "pageStatsBody", style: "height: auto; display: none"}),

    update: function(pages)
    {
        if (!this.isVisible())
            return;

        this.cleanUp();

        // Get schema type for timings.
        var phases = HarSchema.timingsType.properties;

        // If there is no selection, display stats for all pages/files.
        if (!pages.length)
            pages.push(null);

        // Iterate over all selected pages
        for (var j=0; j<pages.length; j++)
        {
            var page = pages[j];

            // Iterate over all requests and compute stats.
            var entries = page? this.model.getPageEntries(page) : this.model.getAllEntries();
            for (var i=0; i<entries.length; i++)
            {
                var entry = entries[i];
                if (!entry.timings)
                    continue;

                // Get timing info (SSL is new in HAR 1.2)
                timingPie.data[0].value += entry.timings.blocked;
                timingPie.data[1].value += entry.timings.dns;
                timingPie.data[2].value += entry.timings.ssl > 0 ? entry.timings.ssl : 0;
                timingPie.data[3].value += entry.timings.connect;
                timingPie.data[4].value += entry.timings.send;
                timingPie.data[5].value += entry.timings.wait;
                timingPie.data[6].value += entry.timings.receive;

                // The ssl time is also included in the connect field, see HAR 1.2 spec
                // (to ensure backward compatibility with HAR 1.1).
                if (entry.timings.ssl > 0)
                    timingPie.data[3].value -= entry.timings.ssl;

                var response = entry.response;
                var resBodySize = response.bodySize > 0 ? response.bodySize : 0;

                // Get Content type info. Make sure we read the right content type
                // even if there is also a charset specified.
                var mimeType = response.content.mimeType;
                var contentType = mimeType ? mimeType.match(/^([^;]+)/)[1] : null;
                var mimeType = contentType ? contentType : response.content.mimeType;

                // Collect response sizes according to the mimeType.
                if (htmlTypes[mimeType]) {
                    contentPie.data[0].value += resBodySize;
                    contentPie.data[0].count++;
                }
                else if (jsTypes[mimeType]) {
                    contentPie.data[1].value += resBodySize;
                    contentPie.data[1].count++;
                }
                else if (cssTypes[mimeType]) {
                    contentPie.data[2].value += resBodySize;
                    contentPie.data[2].count++;
                }
                else if (imageTypes[mimeType]) {
                    contentPie.data[3].value += resBodySize;
                    contentPie.data[3].count++;
                }
                else if (flashTypes[mimeType]) {
                    contentPie.data[4].value += resBodySize;
                    contentPie.data[4].count++;
                }
                else {
                    contentPie.data[5].value += resBodySize;
                    contentPie.data[5].count++;
                }

                // Get traffic info
                trafficPie.data[0].value += entry.request.headersSize > 0 ? entry.request.headersSize : 0;
                trafficPie.data[1].value += entry.request.bodySize > 0 ? entry.request.bodySize : 0;
                trafficPie.data[2].value += entry.response.headersSize > 0 ? entry.response.headersSize : 0;
                trafficPie.data[3].value += resBodySize;

                // Get Cache info
                if (entry.response.status == 206) { // Partial content
                    cachePie.data[1].value += resBodySize;
                    cachePie.data[1].count++;
                }
                else if (entry.response.status == 304) { // From cache
                    cachePie.data[2].value += resBodySize;
                    cachePie.data[2].count++;
                }
                else if (resBodySize > 0){ // Downloaded
                    cachePie.data[0].value += resBodySize;
                    cachePie.data[0].count++;
                }
            }
        }

        // Draw all graphs.
        Pie.draw(Lib.$(this.timingPie, "pieGraph"), timingPie);
        Pie.draw(Lib.$(this.contentPie, "pieGraph"), contentPie);
        Pie.draw(Lib.$(this.trafficPie, "pieGraph"), trafficPie);
        Pie.draw(Lib.$(this.cachePie, "pieGraph"), cachePie);
    },

    cleanUp: function()
    {
        timingPie.cleanUp();
        contentPie.cleanUp();
        trafficPie.cleanUp();
        cachePie.cleanUp();
    },

    showInfoTip: function(infoTip, target, x, y)
    {
        return Pie.showInfoTip(infoTip, target, x, y);
    },

    // * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
    // Timeline Listener

    onSelectionChange: function(pages)
    {
        this.update(pages);
    },

    // * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
    // Public

    show: function(animation)
    {
        if (this.isVisible())
            return;

        InfoTip.addListener(this);
        Lib.setClass(this.element, "opened");

        if (!animation)
            this.element.style.display = "block";
        else
            $(this.element).slideDown();

        var pages = this.timeline.getSelection();
        this.update(pages);
    },

    hide: function(animation)
    {
        if (!this.isVisible())
            return;

        InfoTip.removeListener(this);
        Lib.removeClass(this.element, "opened");

        if (!animation)
            this.element.style.display = "none";
        else
            $(this.element).slideUp();
    },

    isVisible: function()
    {
        return Lib.hasClass(this.element, "opened");
    },

    toggle: function(animation)
    {
        if (this.isVisible())
            this.hide(animation);
        else
            this.show(animation);
    },

    render: function(parentNode)
    {
        this.element = this.tag.replace({}, parentNode);

        // Generate HTML for pie graphs.
        this.timingPie = Pie.render(timingPie, this.element);
        this.contentPie = Pie.render(contentPie, this.element);
        this.trafficPie = Pie.render(trafficPie, this.element);
        this.cachePie = Pie.render(cachePie, this.element);

        // This graph is the last one so remove the separator right border
        this.cachePie.style.borderRight = 0;

        return this.element;
    }
});

//*************************************************************************************************

var Pie = domplate(
{
    tag:
        TABLE({"class": "pagePieTable", cellpadding: 0, cellspacing: 0,
            _repObject: "$pie"},
            TBODY(
                TR(
                    TD({"class": "pieBox", title: "$pie.title"}),
                    TD(
                        FOR("item", "$pie.data",
                            DIV({"class": "pieLabel", _repObject: "$item"},
                                SPAN({"class": "box", style: "background-color: $item.color"}, "&nbsp;"),
                                SPAN({"class": "label"}, "$item.label")
                            )
                        )
                    )
                )
            )
        ),

    render: function(pie, parentNode)
    {
        var root = this.tag.append({pie: pie}, parentNode);

        // Excanvas doesn't support creating CANVAS elements dynamically using
        // HTML injection (and so domplate can't be used). So, create the element
        // using DOM API.
        var pieBox = Lib.$(root, "pieBox");
        var el = document.createElement("canvas");

        //xxxHonza: the class name requires a space at the end in order to hasClass
        // to work. This is terrible hack. Please fix me!
        el.setAttribute("class", "pieGraph ");
        el.setAttribute("height", "100");
        el.setAttribute("width", "100");
        pieBox.appendChild(el);

        if (typeof(G_vmlCanvasManager) != "undefined")
            G_vmlCanvasManager.initElement(el);

        return root;
    },

    draw: function(canvas, pie)
    {
        if (!canvas || !canvas.getContext)
            return;

        var ctx = canvas.getContext("2d");
        var radius = Math.min(canvas.width, canvas.height) / 2;
        var center = [canvas.width/2, canvas.height/2];
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        var sofar = 0; // keep track of progress

        var data = pie.data;
        var total = 0;
        for (var i in data)
            total += data[i].value;

        if (!total)
        {
            ctx.beginPath();
            ctx.moveTo(center[0], center[1]); // center of the pie
            ctx.arc(center[0], center[1], radius, 0, Math.PI * 2, false)
            ctx.closePath();
            ctx.fillStyle = "rgb(229,236,238)";
            ctx.lineStyle = "lightgray";
            ctx.fill();
            return;
        }

        for (var i=0; i<data.length; i++)
        {
            var thisvalue = data[i].value / total;

            ctx.beginPath();
            ctx.moveTo(center[0], center[1]);
            ctx.arc(center[0], center[1], radius,
                Math.PI * (- 0.5 + 2 * sofar), // -0.5 sets set the start to be top
                Math.PI * (- 0.5 + 2 * (sofar + thisvalue)),
                false);

            // line back to the center
            ctx.lineTo(center[0], center[1]);
            ctx.closePath();
            ctx.fillStyle = data[i].color;
            ctx.fill();

            sofar += thisvalue; // increment progress tracker
        }
    },

    showInfoTip: function(infoTip, target, x, y)
    {
        var pieTable = Lib.getAncestorByClass(target, "pagePieTable");
        if (!pieTable)
            return false;

        var label = Lib.getAncestorByClass(target, "pieLabel");
        if (label)
        {
            PieInfoTip.render(pieTable.repObject, label.repObject, infoTip);
            return true;
        }
    }
});

//*************************************************************************************************

var PieInfoTip = domplate(
{
    tag:
        DIV({"class": "pieLabelInfoTip"},
            "$text"
        ),

    getText: function(item)
    {
        return item.label + ": " + formatTime(item.value);
    },

    render: function(pie, item, parentNode)
    {
        var text = pie.getLabelTooltipText(item);
        this.tag.replace({text: text}, parentNode);
    }
});

//*************************************************************************************************

return Stats;

//*************************************************************************************************
}});

/* See license.txt for terms of usage */

define(
'nls/requestList',{
    "root": {
        "fromCache": "From Cache",
        "menuBreakLayout": "Break Timeline Layout",
        "menuOpenRequestInWindow": "Open Request in New Window",
        "menuOpenResponseInWindow": "Open Response in New Window",
        "request": "Request",
        "requests": "Requests",

        "tooltipSize": "%S (%S bytes)",
        "tooltipZippedSize": "%S (%S bytes) - compressed",
        "tooltipUnzippedSize": "%S (%S bytes) - uncompressed",
        "unknownSize": "Unknown size",

        "request.Started": "Request start time since the beginning",
        "request.phases.label": "Request phases start and elapsed time relative to the request start:",
        "request.phase.Resolving": "DNS Lookup",
        "request.phase.Connecting": "Connecting",
        "request.phase.Blocking": "Blocking",
        "request.phase.Sending": "Sending",
        "request.phase.Waiting": "Waiting",
        "request.phase.Receiving": "Receiving",

        "request.timings.label": "Event timing relative to the request start:",
        "ContentLoad": "DOM Loaded",
        "WindowLoad": "Page Loaded",
        "page.event.Load": "Page Loaded",

        "menuBreakTimeline": "Break Timeline Layout",
        "menuOpenRequest": "Open Request in New Window",
        "menuOpenResponse": "Open Response in New Window"
    }
});


/* See license.txt for terms of usage */

define(
'nls/requestBody',{
    "root": {
        "RequestHeaders": "Request Headers",
        "ResponseHeaders": "Response Headers",
        "RequestCookies": "Request Cookies",
        "ResponseCookies": "Response Cookies",
        "URLParameters": "Params",
        "Headers": "Headers",
        "Post": "Post",
        "Put": "Put",
        "Cookies": "Cookies",
        "Response": "Response",
        "Cache": "Cache",
        "HTML": "HTML",
        "DataURL": "Data URL"
    }
});


/* See license.txt for terms of usage */

define("core/dragdrop", [
    "core/lib"
],

function(Lib) {

// ********************************************************************************************* //

/**
 * 
 * @param {Object} element
 * @param {Object} handle
 * @param {Object} callbacks: onDragStart, onDragOver, onDragLeave, onDrop
 */
function Tracker(handle, callbacks)
{
    this.element = handle;
    this.handle = handle;
    this.callbacks = callbacks;

    this.cursorStartPos = null;
    this.cursorLastPos = null;
    //this.elementStartPos = null;
    this.dragging = false;

    // Start listening
    this.onDragStart = Lib.bind(this.onDragStart, this);
    this.onDragOver = Lib.bind(this.onDragOver, this);
    this.onDrop = Lib.bind(this.onDrop, this);

    Lib.addEventListener(this.element, "mousedown", this.onDragStart, false);
    this.active = true;
}

Tracker.prototype =
{
    onDragStart: function(event)
    {
        var e = Lib.fixEvent(event);

        if (this.dragging)
            return;

        if (this.callbacks.onDragStart)
            this.callbacks.onDragStart(this);

        this.dragging = true;
        this.cursorStartPos = absoluteCursorPosition(e);
        this.cursorLastPos = this.cursorStartPos;
        //this.elementStartPos = new Position(
        //    parseInt(this.element.style.left),
        //    parseInt(this.element.style.top));

        Lib.addEventListener(this.element.ownerDocument, "mousemove", this.onDragOver, false);
        Lib.addEventListener(this.element.ownerDocument, "mouseup", this.onDrop, false);

        Lib.cancelEvent(e);
    },

    onDragOver: function(event)
    {
        if (!this.dragging)
            return;

        var e = Lib.fixEvent(event);
        Lib.cancelEvent(e);

        var newPos = absoluteCursorPosition(e);
        //newPos = newPos.Add(this.elementStartPos);
        var newPos = newPos.Subtract(this.cursorStartPos);
        //newPos = newPos.Bound(lowerBound, upperBound);
        //newPos.Apply(this.element);

        // Only fire event if the position has been changed.
        if (this.cursorLastPos.x == newPos.x && this.cursorLastPos.y == newPos.y)
            return;

        if (this.callbacks.onDragOver != null)
        {
            var result = this.callbacks.onDragOver(newPos, this);
            this.cursorLastPos = newPos;
        }

    },

    onDrop: function(event)
    {
        if (!this.dragging)
            return;

        var e = Lib.fixEvent(event);
        Lib.cancelEvent(e);

        this.dragStop();
    },

    dragStop: function()
    {
        if (!this.dragging)
            return;

        Lib.removeEventListener(this.element.ownerDocument, "mousemove", this.onDragOver, false);
        Lib.removeEventListener(this.element.ownerDocument, "mouseup", this.onDrop, false);

        this.cursorStartPos = null;
        this.cursorLastPos = null;
        //this.elementStartPos = null;

        if (this.callbacks.onDrop != null)
            this.callbacks.onDrop(this);

        this.dragging = false;
    },

    destroy: function()
    {
        Lib.removeEventListener(this.element, "mousedown", this.onDragStart, false);
        this.active = false;

        if (this.dragging)
            this.dragStop();
    }
}

// ********************************************************************************************* //

function Position(x, y)
{
    this.x = x;
    this.y = y;

    this.Add = function(val)
    {
        var newPos = new Position(this.x, this.y);
        if (val != null)
        {
            if(!isNaN(val.x))
                newPos.x += val.x;
            if(!isNaN(val.y))
                newPos.y += val.y
        }
        return newPos;
    }
 
    this.Subtract = function(val)
    {
        var newPos = new Position(this.x, this.y);
        if (val != null)
        {
            if(!isNaN(val.x))
                newPos.x -= val.x;
            if(!isNaN(val.y))
                newPos.y -= val.y
        }
        return newPos;
    }

    this.Bound = function(lower, upper)
    {
        var newPos = this.Max(lower);
        return newPos.Min(upper);
    }

    this.Check = function()
    {
        var newPos = new Position(this.x, this.y);
        if (isNaN(newPos.x))
            newPos.x = 0;

        if (isNaN(newPos.y))
            newPos.y = 0;

        return newPos;
    }

    this.Apply = function(element)
    {
        if (typeof(element) == "string")
            element = document.getElementById(element);

        if (!element)
            return;

        if(!isNaN(this.x))
            element.style.left = this.x + "px";

        if(!isNaN(this.y))
            element.style.top = this.y + "px";
    }
}

// ********************************************************************************************* //

function absoluteCursorPosition(e)
{
    if (isNaN(window.scrollX))
    {
        return new Position(e.clientX + document.documentElement.scrollLeft
            + document.body.scrollLeft, e.clientY + document.documentElement.scrollTop
            + document.body.scrollTop);
    }
    else
    {
        return new Position(e.clientX + window.scrollX, e.clientY + window.scrollY);
    }
}

// ********************************************************************************************* //

var DragDrop = {};
DragDrop.Tracker = Tracker;

return DragDrop;

// ********************************************************************************************* //
});


/* See license.txt for terms of usage */

define("preview/requestBody", [
    "domplate/domplate",
    "i18n!nls/requestBody",
    "core/lib",
    "core/cookies",
    "domplate/tabView",
    "core/dragdrop"
],

function(Domplate, Strings, Lib, Cookies, TabView, DragDrop) { with (Domplate) {


var MAX_IMAGE_HEIGHT = 600;

//*************************************************************************************************
// Request Body

/**
 * @domplate This object represents a template for request body that is displayed if a request
 * is expanded within the UI. It's content is composed from set of tabs that are dynamically
 * appended as necessary (depends on actual data).
 *
 * TODO: There should be an APIs allowing to register a new tab from other modules. The same
 * approach as within the Firebug Net panel should be used.
 *
 * There are currently following tabs built-in:
 * {@link HeadersTab}: request and response headers
 * {@link ParamsTab}: URL parameters
 * {@link SentDataTab}: posted data
 * {@link ResponseTab}: request response body
 * {@link CacheTab}: browser cache entry meta-data
 * {@link HtmlTab}: Preview for HTML responses
 * {@link DataURLTab}: Data URLs
 */
function RequestBody() {}
RequestBody.prototype = domplate(
/** @lends RequestBody */
{
    render: function(parentNode, file)
    {
        // Crete tabView and append all necessary tabs.
        var tabView = new TabView("requestBody");
        if (file.response.headers.length > 0)
            tabView.appendTab(new HeadersTab(file));

        if (file.request.queryString && file.request.queryString.length)
            tabView.appendTab(new ParamsTab(file));

        if (file.request.postData)
            tabView.appendTab(new SentDataTab(file, file.request.method));

        if (file.response.content.text && file.response.content.text.length > 0)
            tabView.appendTab(new ResponseTab(file));

        //xxxHonza
        //if (file.request.cookies || file.response.cookies)
        //    tabView.appendTab(new CookiesTab(file));

        if (this.showCache(file))
            tabView.appendTab(new CacheTab(file));

        if (this.showHtml(file))
            tabView.appendTab(new HtmlTab(file));

        if (this.showDataURL(file))
            tabView.appendTab(new DataURLTab(file));

        // Finally, render the tabView and select the first tab by default
        var element = tabView.render(parentNode);
        if (tabView.tabs.length > 0)
            tabView.selectTabByName(tabView.tabs[0].id);

        return element;
    },

    showCache: function(file)
    {
        if (!file.cache)
            return false;

        if (!file.cache.afterRequest)
            return false;

        // Don't show cache tab for images 
        // xxxHonza: the tab could display the image. 
        if (file.category == "image")
            return false;

        return true;
    },

    showHtml: function(file)
    {
        // The mime-type value doesn't have to match the content type exactly
        // there can be a charset specified. So, check the prefix.
        var mimeType = file.response.content.mimeType || "";
        var fileMimeType = file.mimeType || "";
        return (Lib.startsWith(mimeType, "text/html")) ||
            (Lib.startsWith(fileMimeType, "application/xhtml+xml"));
    },

    showDataURL: function(file)
    {
        return file.request.url.indexOf("data:") == 0;
    }
});

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

function HeadersTab(file)
{
    this.file = file;
}

HeadersTab.prototype = domplate(TabView.Tab.prototype,
{
    id: "Headers",
    label: Strings.Headers,

    bodyTag:
        TABLE({"class": "netInfoHeadersText netInfoText netInfoHeadersTable",
                cellpadding: 0, cellspacing: 0},
            TBODY(
                TR({"class": "netInfoResponseHeadersTitle"},
                    TD({colspan: 2},
                        DIV({"class": "netInfoHeadersGroup"}, Strings.ResponseHeaders)
                    )
                ),
                TR({"class": "netInfoRequestHeadersTitle"},
                    TD({colspan: 2},
                        DIV({"class": "netInfoHeadersGroup"}, Strings.RequestHeaders)
                    )
                )
            )
        ),

    headerDataTag:
        FOR("param", "$headers",
            TR(
                TD({"class": "netInfoParamName"}, "$param.name"),
                TD({"class": "netInfoParamValue"},
                    PRE("$param|getParamValue")
                )
            )
        ),

    getParamValue: function(param)
    {
        // This value is inserted into PRE element and so, make sure the HTML isn't escaped (1210).
        // This is why the second parameter is true.
        // The PRE element preserves whitespaces so they are displayed the same, as they come from
        // the server (1194).
        return Lib.wrapText(param.value, true);
    },

    onUpdateBody: function(tabView, body)
    {
        if (this.file.response.headers)
            this.insertHeaderRows(body, this.file.response.headers, "Headers", "ResponseHeaders");

        if (this.file.request.headers)
            this.insertHeaderRows(body, this.file.request.headers, "Headers", "RequestHeaders");
    },

    insertHeaderRows: function(parentNode, headers, tableName, rowName)
    {
        var headersTable = Lib.getElementByClass(parentNode, "netInfo"+tableName+"Table");
        var titleRow = Lib.getElementByClass(headersTable, "netInfo" + rowName + "Title");

        if (headers.length)
        {
            this.headerDataTag.insertRows({headers: headers}, titleRow ? titleRow : parentNode);
            Lib.removeClass(titleRow, "collapsed");
        }
        else
        {
            Lib.setClass(titleRow, "collapsed");
        }
    }
});

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

function ResponseTab(file)
{
    this.file = file;
}

ResponseTab.prototype = domplate(TabView.Tab.prototype,
{
    id: "Response",
    label: Strings.Response,

    bodyTag:
        DIV({"class": "netInfoResponseText netInfoText"},
            PRE({"class": "javascript:nocontrols:nogutter:", name: "code"})
        ),

    onUpdateBody: function (tabView, body) {
        var responseTextBox = Lib.getElementByClass(body, "netInfoResponseText");

        Lib.clearNode(responseTextBox.firstChild);

        var text = this.file.response.content.text;
        var mimeType = this.file.response.content.mimeType;
        var encoding = this.file.response.content.encoding;

        if (mimeType.indexOf('image/') !== -1 && encoding === 'base64') {
            var responseImage = document.createElement('IMG');
            responseImage.src = 'data:' + mimeType + ';base64,' + text;

            if (responseImage.height > MAX_IMAGE_HEIGHT) {
                responseImage.style.height = MAX_IMAGE_HEIGHT + 'px';
            }
            responseTextBox.style.backgroundColor = '#d6d6d6';
            responseTextBox.appendChild(responseImage, responseTextBox);
        } else {
            Lib.insertWrappedText(text, responseTextBox.firstChild);
        }
    }
});

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

function ParamsTab(file)
{
    this.file = file;
}

ParamsTab.prototype = domplate(HeadersTab.prototype,
{
    id: "Params",
    label: Strings.URLParameters,

    bodyTag:
        TABLE({"class": "netInfoParamsText netInfoText netInfoParamsTable",
            cellpadding: 0, cellspacing: 0}, TBODY()
        ),

    onUpdateBody: function(tabView, body)
    {
        if (this.file.request.queryString)
        {
            var textBox = Lib.getElementByClass(body, "netInfoParamsText");
            this.insertHeaderRows(textBox, this.file.request.queryString, "Params");
        }
    }
});

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

function SentDataTab(file, method)
{
    // Convert to lower case and capitalize the first letter.
    method = method.charAt(0).toUpperCase() + method.slice(1).toLowerCase();

    this.file = file;
    this.id =  method;
    this.label = Strings[method];
}

SentDataTab.prototype = domplate(HeadersTab.prototype,
{
    bodyTag:
        DIV({"class": "netInfo$tab.id\\Text netInfoText"},
            TABLE({"class": "netInfo$tab.id\\Table", cellpadding: 0, cellspacing: 0},
                TBODY()
            )
        ),

    onUpdateBody: function(tabView, body)
    {
        var postData = this.file.request.postData;
        if (!postData)
            return;

        var textBox = Lib.getElementByClass(body, "netInfo" + this.id + "Text");
        if (postData.mimeType == "application/x-www-form-urlencoded")
            this.insertHeaderRows(textBox, postData.params, this.id);
        else
            Lib.insertWrappedText(postData.text, textBox);
    }
});

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

function CookiesTab(file)
{
    this.file = file;
}

CookiesTab.prototype = domplate(HeadersTab.prototype,
{
    id: "Cookies",
    label: Strings.Cookies,

    bodyTag:
        DIV({"class": "netInfoCookiesText netInfoText"},
            TABLE({"class": "netInfoCookiesTable", cellpadding: 0, cellspacing: 0},
                TBODY(
                    TR({"class": "netInfoResponseCookiesTitle"},
                        TD({colspan: 2},
                            DIV({"class": "netInfoCookiesGroup"}, Strings.ResponseCookies)
                        )
                    ),
                    TR({"class": "netInfoRequestCookiesTitle"},
                        TD({colspan: 2},
                            DIV({"class": "netInfoCookiesGroup"}, Strings.RequestCookies)
                        )
                    )
                )
            )
        ),

    onUpdateBody: function(tabView, body)
    {
        if (file.response.cookies)
        {
            var textBox = Lib.getElementByClass(body, "netInfoParamsText");
            this.insertHeaderRows(textBox, file.response.cookies, "Cookies", "ResponseCookies");
        }

        if (file.request.cookies)
        {
            var textBox = Lib.getElementByClass(body, "netInfoParamsText");
            this.insertHeaderRows(textBox, file.request.cookies, "Cookies", "RequestCookies");
        }
    }
});

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

function CacheTab(file)
{
    this.file = file;
}

CacheTab.prototype = domplate(HeadersTab.prototype,
{
    id: "Cache",
    label: Strings.Cache,

    bodyTag:
        DIV({"class": "netInfoCacheText netInfoText"},
            TABLE({"class": "netInfoCacheTable", cellpadding: 0, cellspacing: 0},
                TBODY()
            )
        ),

    onUpdateBody: function(tabView, body)
    {
        if (this.file.cache && this.file.cache.afterRequest)
        {
            var cacheEntry = this.file.cache.afterRequest;

            var values = [];
            for (var prop in cacheEntry)
                values.push({name: prop, value: cacheEntry[prop]});

            this.insertHeaderRows(body, values, "Cache");
        }
    }
});

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

/**
 * @domplate Represents an HTML preview for network responses using 'text/html' or 
 * 'application/xhtml+xml' mime type.
 */
function HtmlTab(file)
/** @lends HtmlTab */
{
    this.file = file;
}

HtmlTab.prototype = domplate(HeadersTab.prototype,
{
    id: "HTML",
    label: Strings.HTML,

    bodyTag:
        DIV({"class": "netInfoHtmlText netInfoText"},
            IFRAME({"class": "netInfoHtmlPreview", onload: "$onLoad"}),
            DIV({"class": "htmlPreviewResizer"})
        ),

    onUpdateBody: function(tabView, body)
    {
        this.preview = Lib.getElementByClass(body, "netInfoHtmlPreview");

        var height = parseInt(Cookies.getCookie("htmlPreviewHeight"));
        if (!isNaN(height))
            this.preview.style.height = height + "px";

        var handler = Lib.getElementByClass(body, "htmlPreviewResizer");
        this.resizer = new DragDrop.Tracker(handler, {
            onDragStart: Lib.bind(this.onDragStart, this),
            onDragOver: Lib.bind(this.onDragOver, this),
            onDrop: Lib.bind(this.onDrop, this)
        });
    },

    onLoad: function(event)
    {
        var e = Lib.fixEvent(event);
        var self = Lib.getAncestorByClass(e.target, "tabHTMLBody").repObject;
        self.preview.contentWindow.document.body.innerHTML = self.file.response.content.text;
    },

    onDragStart: function(tracker)
    {
        var body = Lib.getBody(this.preview.ownerDocument);
        body.setAttribute("hResizing", "true");
        this.startHeight = this.preview.clientHeight;
    },

    onDragOver: function(newPos, tracker)
    {
        var newHeight = (this.startHeight + newPos.y);
        this.preview.style.height = newHeight + "px";
        Cookies.setCookie("htmlPreviewHeight", newHeight);
    },

    onDrop: function(tracker)
    {
        var body = Lib.getBody(this.preview.ownerDocument);
        body.removeAttribute("hResizing");
    }
});

/**
 * @domplate Represents a request body tab displaying unescaped data: URLs.
 */
function DataURLTab(file)
/** @lends DataURLTab */
{
    this.file = file;
}

DataURLTab.prototype = domplate(HeadersTab.prototype,
{
    id: "DataURL",
    label: Strings.DataURL,

    bodyTag:
        DIV({"class": "netInfoDataURLText netInfoText"}),

    onUpdateBody: function(tabView, body)
    {
        var textBox = Lib.getElementByClass(body, "netInfoDataURLText");
        var data = this.file.request.url;

        if (data.indexOf("data:image") == 0)
        {
            var image = body.ownerDocument.createElement("img");
            image.src = data;
            textBox.appendChild(image);
        }
        else
        {
            Lib.insertWrappedText(unescape(data), textBox);
        }
    }
});

//*************************************************************************************************

return RequestBody;

//*************************************************************************************************
}});

/* See license.txt for terms of usage */

define("preview/requestList", [
    "domplate/domplate",
    "core/lib",
    "i18n!nls/requestList",
    "preview/harModel",
    "core/cookies",
    "preview/requestBody",
    "domplate/infoTip",
    "domplate/popupMenu"
],

function(Domplate, Lib, Strings, HarModel, Cookies, RequestBody, InfoTip, Menu) {
with (Domplate) {

// ********************************************************************************************* //
// Request List

function RequestList(input)
{
    this.input = input;
    this.pageTimings = [];

    // List of pageTimings fields (see HAR 1.2 spec) that should be displayed
    // in the waterfall graph as vertical lines. The HAR spec defines two timings:
    // onContentLoad: DOMContentLoad event fired
    // onLoad: load event fired
    // New custom page timing fields can be appended using RequestList.addPageTiming method.
    this.addPageTiming({
        name: "onContentLoad",
        classes: "netContentLoadBar",
        description: Strings["ContentLoad"]
    });

    this.addPageTiming({
        name: "onLoad",
        classes: "netWindowLoadBar",
        description: Strings["WindowLoad"]
    });

    InfoTip.addListener(this);
}

// ********************************************************************************************* //
// Columns 

/**
 * List of all available columns for the request table, see also RequestList.prototype.tableTag
 */
RequestList.columns = [
    "url",
    "status",
    "type",
    "domain",
    "size",
    "timeline"
];

/**
 * List of columns that are visible by default.
 */
RequestList.defaultColumns = [
    "url",
    "status",
    "size",
    "timeline"
];

/**
 * Use this method to get a list of currently visible columns.
 */
RequestList.getVisibleColumns = function()
{
    var cols = Cookies.getCookie("previewCols");
    if (cols)
    {
        // Columns names are separated by a space so, make sure to properly process
        // spaces in the cookie value.
        cols = cols.replace(/\+/g, " ");
        cols = unescape(cols);
        return cols.split(" ");
    }

    if (!cols)
    {
        var content = document.getElementById("content");
        if (content)
        {
            cols = content.getAttribute("previewCols");
            if (cols)
                return cols.split(" ");
        }
    }

    return Lib.cloneArray(RequestList.defaultColumns);
}

RequestList.setVisibleColumns = function(cols, avoidCookies)
{
    if (!cols)
        cols = RequestList.getVisibleColumns();

    // If the parameter is an array, convert it to string.
    if (cols.join)
        cols = cols.join(" ");

    var content = document.getElementById("content");
    if (content)
        content.setAttribute("previewCols", cols);

    // Update cookie
    if (!avoidCookies)
        Cookies.setCookie("previewCols", cols);
}

// Initialize UI. List of columns is specified on the content element (used by CSS).
RequestList.setVisibleColumns();

// ********************************************************************************************* //

/**
 * @domplate This object represents a template for list of entries (requests).
 * This list is displayed when a page is expanded by the user. 
 */
RequestList.prototype = domplate(
/** @lends RequestList */
{
    tableTag:
        TABLE({"class": "netTable", cellpadding: 0, cellspacing: 0, onclick: "$onClick",
            _repObject: "$requestList"},
            TBODY(
                TR({"class" : "netSizerRow"},
                    TD({"class": "netHrefCol netCol", width: "20%"}),
                    TD({"class": "netStatusCol netCol", width: "7%"}),
                    TD({"class": "netTypeCol netCol", width: "7%"}),
                    TD({"class": "netDomainCol netCol", width: "7%"}),
                    TD({"class": "netSizeCol netCol", width: "7%"}),
                    TD({"class": "netTimeCol netCol", width: "100%"}),
                    TD({"class": "netOptionsCol netCol", width: "15px"}) // Options
                )
            )
        ),

    fileTag:
        FOR("file", "$files",
            TR({"class": "netRow loaded",
                $isExpandable: "$file|isExpandable",
                $responseError: "$file|isError",
                $responseRedirect: "$file|isRedirect",
                $fromCache: "$file|isFromCache"},
                TD({"class": "netHrefCol netCol"},
                    DIV({"class": "netHrefLabel netLabel",
                         style: "margin-left: $file|getIndent\\px"},
                        "$file|getHref"
                    ),
                    DIV({"class": "netFullHrefLabel netHrefLabel netLabel",
                         style: "margin-left: $file|getIndent\\px"},
                        "$file|getFullHref"
                    )
                ),
                TD({"class": "netStatusCol netCol"},
                    DIV({"class": "netStatusLabel netLabel"}, "$file|getStatus")
                ),
                TD({"class": "netTypeCol netCol"},
                    DIV({"class": "netTypeLabel netLabel"}, "$file|getType")
                ),
                TD({"class": "netDomainCol netCol"},
                    DIV({"class": "netDomainLabel netLabel"}, "$file|getDomain")
                ),
                TD({"class": "netSizeCol netCol"},
                    DIV({"class": "netSizeLabel netLabel"}, "$file|getSize")
                ),
                TD({"class": "netTimeCol netCol"},
                    DIV({"class": "netTimelineBar"},
                        "&nbsp;",
                        DIV({"class": "netBlockingBar netBar"}),
                        DIV({"class": "netResolvingBar netBar"}),
                        DIV({"class": "netConnectingBar netBar"}),
                        DIV({"class": "netSendingBar netBar"}),
                        DIV({"class": "netWaitingBar netBar"}),
                        DIV({"class": "netReceivingBar netBar"},
                            SPAN({"class": "netTimeLabel"}, "$file|getElapsedTime")
                        )
                        // Page timings (vertical lines) are dynamically appended here.
                    )
                ),
                TD({"class": "netOptionsCol netCol"},
                    DIV({"class": "netOptionsLabel netLabel", onclick: "$onOpenOptions"})
                )
            )
        ),

    headTag:
        TR({"class": "netHeadRow"},
            TD({"class": "netHeadCol", colspan: 7},
                DIV({"class": "netHeadLabel"}, "$doc.rootFile.href")
            )
        ),

    netInfoTag:
        TR({"class": "netInfoRow"},
            TD({"class": "netInfoCol", colspan: 7})
        ),

    summaryTag:
        TR({"class": "netRow netSummaryRow"},
            TD({"class": "netHrefCol netCol"},
                DIV({"class": "netCountLabel netSummaryLabel"}, "-")
            ),
            TD({"class": "netStatusCol netCol"}),
            TD({"class": "netTypeCol netCol"}),
            TD({"class": "netDomainCol netCol"}),
            TD({"class": "netTotalSizeCol netSizeCol netCol"},
                DIV({"class": "netTotalSizeLabel netSummaryLabel"}, "0KB")
            ),
            TD({"class": "netTotalTimeCol netTimeCol netCol"},
                DIV({"class": "", style: "width: 100%"},
                    DIV({"class": "netCacheSizeLabel netSummaryLabel"},
                        "(",
                        SPAN("0KB"),
                        SPAN(" " + Strings.fromCache),
                        ")"
                    ),
                    DIV({"class": "netTimeBar"},
                        SPAN({"class": "netTotalTimeLabel netSummaryLabel"}, "0ms")
                    )
                )
            ),
            TD({"class": "netOptionsCol netCol"})
        ),

    getIndent: function(file)
    {
        return 0;
    },

    isError: function(file)
    {
        var errorRange = Math.floor(file.response.status/100);
        return errorRange == 4 || errorRange == 5;
    },

    isRedirect: function(file)
    {
        // xxxHonza: 304?
        //var errorRange = Math.floor(file.response.status/100);
        //return errorRange == 3;
        return false;
    },

    isFromCache: function(file)
    {
        return file.cache && file.cache.afterRequest;
    },

    getHref: function(file)
    {
        var fileName = Lib.getFileName(this.getFullHref(file));
        return unescape(file.request.method + " " + fileName);
    },

    getFullHref: function(file)
    {
        return unescape(file.request.url);
    },

    getStatus: function(file)
    {
        var status = file.response.status > 0 ? (file.response.status + " ") : "";
        return status + file.response.statusText;
    },

    getType: function(file)
    {
        return file.response.content.mimeType;
    },

    getDomain: function(file)
    {
        return Lib.getPrettyDomain(file.request.url);
    },

    getSize: function(file)
    {
        var bodySize = file.response.bodySize;
        var size = (bodySize && bodySize != -1) ? bodySize :
            file.response.content.size;

        return this.formatSize(size);
    },

    isExpandable: function(file)
    {
        var hasHeaders = file.response.headers.length > 0;
        var hasDataURL = file.request.url.indexOf("data:") == 0;
        return hasHeaders || hasDataURL;
    },

    formatSize: function(bytes)
    {
        return Lib.formatSize(bytes);
    },

    getElapsedTime: function(file)
    {
        // Total request time doesn't include the time spent in queue.
        //var elapsed = file.time - file.timings.blocked;
        var time = Math.round(file.time * 10) / 10;
        return Lib.formatTime(time);
    },

    // * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

    onClick: function(event)
    {
        var e = Lib.fixEvent(event);
        if (Lib.isLeftClick(event))
        {
            var row = Lib.getAncestorByClass(e.target, "netRow");
            if (row)
            {
                this.toggleHeadersRow(row);
                Lib.cancelEvent(event);
            }
        }
        else if (Lib.isControlClick(event))
        {
            window.open(event.target.innerText || event.target.textContent);
        }
    },

    toggleHeadersRow: function(row)
    {
        if (!Lib.hasClass(row, "isExpandable"))
            return;

        var file = row.repObject;

        Lib.toggleClass(row, "opened");
        if (Lib.hasClass(row, "opened"))
        {
            var netInfoRow = this.netInfoTag.insertRows({}, row)[0];
            netInfoRow.repObject = file;

            var requestBody = new RequestBody();
            requestBody.render(netInfoRow.firstChild, file);
        }
        else
        {
            var netInfoRow = row.nextSibling;
            var netInfoBox = Lib.getElementByClass(netInfoRow, "netInfoBody");
            row.parentNode.removeChild(netInfoRow);
        }
    },

    // * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
    // Options

    onOpenOptions: function(event)
    {
        var e = Lib.fixEvent(event);
        Lib.cancelEvent(event);

        if (!Lib.isLeftClick(event))
            return;

        var target = e.target;

        // Collect all menu items.
        var row = Lib.getAncestorByClass(target, "netRow");
        var items = this.getMenuItems(row);
        if (!items.length)
            return;

        // Finally, display the the popup menu.
        // xxxHonza: the old <DIV> can be still visible.
        var menu = new Menu({id: "requestContextMenu", items: items});
        menu.showPopup(target);
    },

    // * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
    // Menu Definition

    getMenuItems: function(row)
    {
        var file = row.repObject;
        var phase = row.phase;

        // Disable the 'break layout' command for the first file in the first phase.
        var disableBreakLayout = (phase.files[0] == file && this.phases[0] == phase);

        var items = [
            {
                label: Strings.menuBreakTimeline,
                type: "checkbox",
                disabled: disableBreakLayout,
                checked: phase.files[0] == file && !disableBreakLayout,
                command: Lib.bind(this.breakLayout, this, row)
            },
            "-",
            {
                label: Strings.menuOpenRequest,
                command: Lib.bind(this.openRequest, this, file)
            },
            {
                label: Strings.menuOpenResponse,
                disabled: !file.response.content.text,
                command: Lib.bind(this.openResponse, this, file)
            }
        ];

        // Distribute to all listeners to allow registering custom commands.
        // Listeneres are set by the parent page-list.
        Lib.dispatch(this.listeners, "getMenuItems", [items, this.input, file]);

        return items;
    },

    // * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
    // Command Handlers

    openRequest: function(event, file)
    {
        window.open(file.request.url);
    },

    openResponse: function(event, file)
    {
        var response = file.response.content.text;
        var mimeType = file.response.content.mimeType;
        var encoding = file.response.content.encoding;
        var url = "data:" + (mimeType ? mimeType: "") + ";" +
            (encoding ? encoding : "") + "," + response;

        window.open(url);
    },

    breakLayout: function(event, row)
    {
        var file = row.repObject;
        var phase = row.phase;
        var layoutBroken = phase.files[0] == file;
        row.breakLayout = !layoutBroken;

        // For CSS (visual separator between two phases).
        row.setAttribute("breakLayout", row.breakLayout ? "true" : "false");

        var netTable = Lib.getAncestorByClass(row, "netTable");
        var page = HarModel.getParentPage(this.input, file);
        this.updateLayout(netTable, page);
    },

    // * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
    // Layout

    updateLayout: function(table, page)
    {
        var requests = HarModel.getPageEntries(this.input, page);

        this.table = table;
        var tbody = this.table.firstChild;
        var row = this.firstRow = tbody.firstChild.nextSibling;

        this.phases = [];

        // The phase interval is customizable through a cookie.
        var phaseInterval = Cookies.getCookie("phaseInterval");
        if (!phaseInterval)
            phaseInterval = 4000;

        var phase = null;

        var pageStartedDateTime = page ? Lib.parseISO8601(page.startedDateTime) : null;

        // The onLoad time stamp is used for proper initialization of the first phase. The first
        // phase contains all requests till onLoad is fired (even if there are time gaps).
        // Don't worry if it
        var onLoadTime = (page && page.pageTimings) ? page.pageTimings["onLoad"] : -1;

        // The timing could be NaN or -1. In such case keep the value otherwise
        // make the time absolute.
        if (onLoadTime > 0)
            onLoadTime += pageStartedDateTime;

        // Iterate over all requests and create phases.
        for (var i=0; i<requests.length; i++)
        {
            var file = requests[i];

            if (Lib.hasClass(row, "netInfoRow"))
                row = row.nextSibling;

            row.repObject = file;

            // If the parent page doesn't exists get startedDateTime of the
            // first request.
            if (!pageStartedDateTime)
                pageStartedDateTime = Lib.parseISO8601(file.startedDateTime);

            var startedDateTime = Lib.parseISO8601(file.startedDateTime);
            var phaseLastStartTime = phase ? Lib.parseISO8601(phase.getLastStartTime()) : 0;
            var phaseEndTime = phase ? phase.endTime : 0;

            // New phase is started if:
            // 1) There is no phase yet.
            // 2) There is a gap between this request and the last one.
            // 3) The new request is not started during the page load.
            var newPhase = false;
            if (phaseInterval >= 0)
            {
                newPhase = (startedDateTime > onLoadTime) &&
                    ((startedDateTime - phaseLastStartTime) >= phaseInterval) &&
                    (startedDateTime + file.time >= phaseEndTime + phaseInterval);
            }

            // 4) The file can be also marked with breakLayout
            if (typeof(row.breakLayout) == "boolean")
            {
                if (!phase || row.breakLayout)
                    phase = this.startPhase(file);
                else
                    phase.addFile(file);
            }
            else
            {
                if (!phase || newPhase)
                    phase = this.startPhase(file);
                else
                    phase.addFile(file);
            }

            // For CSS (visual separator between two phases). Except of the first file
            // in the first phase.
            if (this.phases[0] != phase)
                row.setAttribute("breakLayout", (phase.files[0] == file) ? "true" : "false");

            if (phase.startTime == undefined || phase.startTime > startedDateTime)
                phase.startTime = startedDateTime;

            // file.time represents total elapsed time of the request.
            if (phase.endTime == undefined || phase.endTime < startedDateTime + file.time)
                phase.endTime = startedDateTime + file.time;

            row = row.nextSibling;
        }

        this.updateTimeStamps(page);
        this.updateTimeline(page);
        this.updateSummaries(page);
    },

    startPhase: function(file)
    {
        var phase = new Phase(file);
        this.phases.push(phase);
        return phase;
    },

    calculateFileTimes: function(page, file, phase)
    {
        if (phase != file.phase)
        {
            phase = file.phase;
            this.phaseStartTime = phase.startTime;
            this.phaseEndTime = phase.endTime;
            this.phaseElapsed = this.phaseEndTime - phase.startTime;
        }

        if (!file.timings)
            return phase;

        // Individual phases of a request:
        //
        // 1) Blocking          HTTP-ON-MODIFY-REQUEST -> (STATUS_RESOLVING || STATUS_CONNECTING_TO)
        // 2) DNS               STATUS_RESOLVING -> STATUS_CONNECTING_TO
        // 3) Connecting        STATUS_CONNECTING_TO -> (STATUS_CONNECTED_TO || STATUS_SENDING_TO)
        // 4) Sending           STATUS_SENDING_TO -> STATUS_WAITING_FOR
        // 5) Waiting           STATUS_WAITING_FOR -> STATUS_RECEIVING_FROM
        // 6) Receiving         STATUS_RECEIVING_FROM -> ACTIVITY_SUBTYPE_RESPONSE_COMPLETE
        //
        // Note that HTTP-ON-EXAMINE-RESPONSE should not be used since the time isn't passed
        // along with this event and so, it could break the timing. Only the HTTP-ON-MODIFY-REQUEST
        // is used to get begining of the request and compute the blocking time. Hopefully this
        // will work or there is better mechanism.
        //
        // If the response comes directly from the browser cache, there is only one state.
        // HTTP-ON-MODIFY-REQUEST -> HTTP-ON-EXAMINE-CACHED-RESPONSE

        // Compute end of each phase since the request start.
        var blocking = ((file.timings.blocked < 0) ? 0 : file.timings.blocked);
        var resolving = blocking + ((file.timings.dns < 0) ? 0 : file.timings.dns);
        var connecting = resolving + ((file.timings.connect < 0) ? 0 : file.timings.connect);
        var sending = connecting + ((file.timings.send < 0) ? 0 : file.timings.send);
        var waiting = sending + ((file.timings.wait < 0) ? 0 : file.timings.wait);
        var receiving = waiting + ((file.timings.receive < 0) ? 0 : file.timings.receive);

        var elapsed = file.time;
        var startedDateTime = Lib.parseISO8601(file.startedDateTime);
        this.barOffset = (((startedDateTime-this.phaseStartTime)/this.phaseElapsed) * 100).toFixed(3);

        // Compute size of each bar. Left side of each bar starts at the 
        // beginning. The first bar is on top of all and the last one is
        // at the bottom (z-index). 
        this.barBlockingWidth = ((blocking/this.phaseElapsed) * 100).toFixed(3);
        this.barResolvingWidth = ((resolving/this.phaseElapsed) * 100).toFixed(3);
        this.barConnectingWidth = ((connecting/this.phaseElapsed) * 100).toFixed(3);
        this.barSendingWidth = ((sending/this.phaseElapsed) * 100).toFixed(3);
        this.barWaitingWidth = ((waiting/this.phaseElapsed) * 100).toFixed(3);
        this.barReceivingWidth = ((receiving/this.phaseElapsed) * 100).toFixed(3);

        // Compute also offset for page timings, e.g.: contentLoadBar and windowLoadBar,
        // which are displayed for the first phase. This is done only if a page exists.
        this.calculatePageTimings(page, file, phase);

        return phase;
    },

    calculatePageTimings: function(page, file, phase)
    {
        // Obviously we need a page object for page timings.
        if (!page)
            return;

        var pageStart = Lib.parseISO8601(page.startedDateTime);

        // Iterate all timings in this phase and generate offsets (px position in the timeline).
        for (var i=0; i<phase.pageTimings.length; i++)
        {
            var time = phase.pageTimings[i].time;
            if (time > 0)
            {
                var timeOffset = pageStart + time - phase.startTime;
                var barOffset = ((timeOffset/this.phaseElapsed) * 100).toFixed(3);
                phase.pageTimings[i].offset = barOffset;
            }
        }
    },

    updateTimeline: function(page)
    {
        var tbody = this.table.firstChild;

        var phase;

        // Iterate over all existing entries. Some rows aren't associated with a file 
        // (e.g. header, sumarry) so, skip them.
        for (var row = this.firstRow; row; row = row.nextSibling)
        {
            var file = row.repObject;
            if (!file)
                continue;

            // Skip expanded rows.
            if (Lib.hasClass(row, "netInfoRow"))
                continue;

            phase = this.calculateFileTimes(page, file, phase);

            // Remember the phase it's utilized by the time info-tip.
            row.phase = file.phase;

            // Remove the phase from the file object so, it's not displayed
            // in the DOM tab.
            delete file.phase;

            // Parent for all timing bars.
            var timelineBar = Lib.getElementByClass(row, "netTimelineBar");

            // Get bar nodes. Every node represents one part of the graph-timeline.
            var blockingBar = timelineBar.children[0];
            var resolvingBar = blockingBar.nextSibling;
            var connectingBar = resolvingBar.nextSibling;
            var sendingBar = connectingBar.nextSibling;
            var waitingBar = sendingBar.nextSibling;
            var receivingBar = waitingBar.nextSibling;

            // All bars starts at the beginning of the appropriate request graph. 
            blockingBar.style.left = 
                connectingBar.style.left =
                resolvingBar.style.left =
                sendingBar.style.left = 
                waitingBar.style.left =
                receivingBar.style.left = this.barOffset + "%";

            // Sets width of all bars (using style). The width is computed according to measured timing.
            blockingBar.style.width = this.barBlockingWidth + "%";
            resolvingBar.style.width = this.barResolvingWidth + "%";
            connectingBar.style.width = this.barConnectingWidth + "%";
            sendingBar.style.width = this.barSendingWidth + "%";
            waitingBar.style.width = this.barWaitingWidth + "%";
            receivingBar.style.width = this.barReceivingWidth + "%";

            // Remove all existing timing bars first. The UI can be relayouting at this moment
            // (can happen if break layout is executed).
            var bars = Lib.getElementsByClass(timelineBar, "netPageTimingBar");
            for (var i=0; i<bars.length; i++)
                bars[i].parentNode.removeChild(bars[i]);

            // Generate UI for page timings (vertical lines displayed for the first phase)
            for (var i=0; i<phase.pageTimings.length; i++)
            {
                var timing = phase.pageTimings[i];
                if (!timing.offset)
                    continue;

                var bar = timelineBar.ownerDocument.createElement("DIV");
                timelineBar.appendChild(bar);

                if (timing.classes)
                    Lib.setClass(bar, timing.classes);

                Lib.setClass(bar, "netPageTimingBar netBar");

                bar.style.left = timing.offset + "%";
                bar.style.display = "block";

                // The offset will be calculated for the next row (request entry) again
                // within calculatePageTimings in the next row (outer) cycle.
                timing.offset = null;
            }
        }
    },

    updateTimeStamps: function(page)
    {
        if (!page)
            return;

        // Convert registered page timings (e.g. onLoad, DOMContentLoaded) into structures
        // with label information.
        var pageTimings = [];
        for (var i=0; page.pageTimings && i<this.pageTimings.length; i++)
        {
            var timing = this.pageTimings[i];
            var eventTime = page.pageTimings[timing.name];
            if (eventTime > 0)
            {
                pageTimings.push({
                    label: timing.name,
                    time: eventTime,
                    classes: timing.classes,
                    comment: timing.description
                });
            }
        }

        // Get time-stamps generated from console.timeStamp() method (this method has been
        // introduced in Firebug 1.8b3).
        // See Firebug documentation: http://getfirebug.com/wiki/index.php/Console_API
        var timeStamps = page.pageTimings ? page.pageTimings._timeStamps : [];

        // Put together all timing info.
        if (timeStamps)
            pageTimings.push.apply(pageTimings, timeStamps);

        // Iterate all existing phases.
        var phases = this.phases;
        for (var i=0; i<phases.length; i++)
        {
            var phase = phases[i];
            var nextPhase = phases[i+1];

            // Iterate all timings and divide them into phases. This process can extend
            // the end of a phase.
            for (var j=0; j<pageTimings.length; j++)
            {
                var stamp = pageTimings[j];
                var time = stamp.time;
                if (!time)
                    continue;

                // We need the absolute time.
                var startedDateTime = Lib.parseISO8601(page.startedDateTime);
                time += startedDateTime;

                // The time stamp belongs to the current phase if:
                // 1) It occurs before the next phase started or there is no next phase.
                if (!nextPhase || time < nextPhase.startTime)
                {
                    // 2) It occurs after the current phase started, or this is the first phase.
                    if (i == 0 || time >= phase.startTime)
                    {
                        // This is the case where the time stamp occurs before the first phase
                        // started (shouldn't actually happen since there can't be a stamp made
                        // before the first document request).
                        if (phase.startTime > time)
                            phase.startTime = time;

                        // This is the case where the time stamp occurs after the phase end time,
                        // but still before the next phase start time.
                        if (phase.endTime < time)
                            phase.endTime = time;

                        phase.pageTimings.push({
                            classes: stamp.classes ? stamp.classes : "netTimeStampBar",
                            name: stamp.label,
                            description: stamp.comment,
                            time: stamp.time
                        });
                    }
                }
            }
        }
    },

    updateSummaries: function(page)
    {
        var phases = this.phases;
        var fileCount = 0, totalSize = 0, cachedSize = 0, totalTime = 0;
        for (var i = 0; i < phases.length; ++i)
        {
            var phase = phases[i];
            phase.invalidPhase = false;

            var summary = this.summarizePhase(phase);
            fileCount += summary.fileCount;
            totalSize += summary.totalSize;
            cachedSize += summary.cachedSize;
            totalTime += summary.totalTime;
        }

        var row = this.summaryRow;
        if (!row)
            return;

        var countLabel = Lib.getElementByClass(row, "netCountLabel");
        countLabel.firstChild.nodeValue = this.formatRequestCount(fileCount);

        var sizeLabel = Lib.getElementByClass(row, "netTotalSizeLabel");
        sizeLabel.setAttribute("totalSize", totalSize);
        sizeLabel.firstChild.nodeValue = Lib.formatSize(totalSize);

        var cacheSizeLabel = Lib.getElementByClass(row, "netCacheSizeLabel");
        cacheSizeLabel.setAttribute("collapsed", cachedSize == 0);
        cacheSizeLabel.childNodes[1].firstChild.nodeValue = Lib.formatSize(cachedSize);

        var timeLabel = Lib.getElementByClass(row, "netTotalTimeLabel");
        var timeText = Lib.formatTime(totalTime);

        // xxxHonza: localization?
        if (page && page.pageTimings.onLoad > 0)
            timeText += " (onload: " + Lib.formatTime(page.pageTimings.onLoad) + ")";

        timeLabel.innerHTML = timeText;
    },

    formatRequestCount: function(count)
    {
        return count + " " + (count == 1 ? Strings.request : Strings.requests);
    },

    summarizePhase: function(phase)
    {
        var cachedSize = 0, totalSize = 0;

        var category = "all";
        if (category == "all")
            category = null;

        var fileCount = 0;
        var minTime = 0, maxTime = 0;

        for (var i=0; i<phase.files.length; i++)
        {
            var file = phase.files[i];
            var startedDateTime = Lib.parseISO8601(file.startedDateTime);

            if (!category || file.category == category)
            {
                ++fileCount;

                var bodySize = file.response.bodySize;
                var size = (bodySize && bodySize != -1) ? bodySize : file.response.content.size;

                totalSize += size;
                if (file.response.status == 304)
                    cachedSize += size;

                if (!minTime || startedDateTime < minTime)
                    minTime = startedDateTime;

                var fileEndTime = startedDateTime + file.time;
                if (fileEndTime > maxTime)
                    maxTime = fileEndTime;
            }
        }

        var totalTime = maxTime - minTime;
        return {cachedSize: cachedSize, totalSize: totalSize, totalTime: totalTime,
            fileCount: fileCount}
    },

    // * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
    // InfoTip

    showInfoTip: function(infoTip, target, x, y)
    {
        // There is more instances of RequestList object registered as info-tips listener
        // so make sure the one that is associated with the target is used.
        var table = Lib.getAncestorByClass(target, "netTable");
        if (!table || table.repObject != this)
            return;

        var row = Lib.getAncestorByClass(target, "netRow");
        if (row)
        {
            if (Lib.getAncestorByClass(target, "netBar"))
            {
                // There is no background image for multiline tooltips.
                infoTip.setAttribute("multiline", true);
                var infoTipURL = row.repObject.startedDateTime + "-nettime"; //xxxHonza the ID should be URL.
                // xxxHonza: there can be requests to the same URLs with different timings.
                //if (infoTipURL == this.infoTipURL)
                //    return true;

                this.infoTipURL = infoTipURL;
                return this.populateTimeInfoTip(infoTip, row);
            }
            else if (Lib.hasClass(target, "netSizeLabel"))
            {
                var infoTipURL = row.repObject.startedDateTime + "-netsize"; //xxxHonza the ID should be URL.
                // xxxHonza: there can be requests to the same URLs with different response sizes.
                //if (infoTipURL == this.infoTipURL)
                //    return true;

                this.infoTipURL = infoTipURL;
                return this.populateSizeInfoTip(infoTip, row);
            }
        }
    },

    populateTimeInfoTip: function(infoTip, row)
    {
        EntryTimeInfoTip.render(this, row, infoTip);
        return true;
    },

    populateSizeInfoTip: function(infoTip, row)
    {
        EntrySizeInfoTip.render(this, row, infoTip);
        return true;
    },

    // * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
    // Public

    render: function(parentNode, page)
    {
        var entries = HarModel.getPageEntries(this.input, page);
        if (!entries.length)
            return null;

        return this.append(parentNode, page, entries);
    },

    append: function(parentNode, page, entries)
    {
        if (!this.table)
            this.table = this.tableTag.replace({requestList: this}, parentNode, this);

        if (!this.summaryRow)
            this.summaryRow = this.summaryTag.insertRows({}, this.table.firstChild)[0];

        var tbody = this.table.firstChild;
        var lastRow = tbody.lastChild.previousSibling;

        var result = this.fileTag.insertRows({files: entries}, lastRow, this);
        this.updateLayout(this.table, page);

        return result[0];
    },

    addPageTiming: function(timing)
    {
        this.pageTimings.push(timing);
    }
});

// ********************************************************************************************* //

/**
 * @object This object represents a phase that joins related requests into groups (phases).
 */
function Phase(file)
{
    this.files = [];
    this.pageTimings = [];

    this.addFile(file);
};

Phase.prototype =
{
    addFile: function(file)
    {
        this.files.push(file);
        file.phase = this;
    },

    getLastStartTime: function()
    {
        // The last request start time.
        return this.files[this.files.length - 1].startedDateTime;
    }
};

//***********************************************************************************************//

/**
 * @domplate This object represents a popup info tip with detailed timing info for an
 * entry (request).
 */
var EntryTimeInfoTip = domplate(
{
    tableTag:
        TABLE({"class": "timeInfoTip"},
            TBODY()
        ),

    timingsTag:
        FOR("time", "$timings",
            TR({"class": "timeInfoTipRow", $collapsed: "$time|hideBar"},
                TD({"class": "$time|getBarClass timeInfoTipBar",
                    $loaded: "$time.loaded",
                    $fromCache: "$time.fromCache"
                }),
                TD({"class": "timeInfoTipCell startTime"},
                    "$time.start|formatStartTime"
                ),
                TD({"class": "timeInfoTipCell elapsedTime"},
                    "$time.elapsed|formatTime"
                ),
                TD("$time|getLabel")
            )
        ),

    startTimeTag:
        TR(
            TD(),
            TD("$startTime.time|formatStartTime"),
            TD({"class": "timeInfoTipStartLabel", "colspan": 2},
                "$startTime|getLabel"
            )
        ),

    separatorTag:
        TR({},
            TD({"class": "timeInfoTipSeparator", "colspan": 4, "height": "10px"},
                SPAN("$label")
            )
        ),

    eventsTag:
        FOR("event", "$events",
            TR({"class": "timeInfoTipEventRow"},
                TD({"class": "timeInfoTipBar", align: "center"},
                    DIV({"class": "$event|getPageTimingClass timeInfoTipEventBar"})
                ),
                TD("$event.start|formatStartTime"),
                TD({"colspan": 2},
                    "$event|getTimingLabel"
                )
            )
        ),

    hideBar: function(obj)
    {
        return !obj.elapsed && obj.bar == "request.phase.Blocking";
    },

    getBarClass: function(obj)
    {
        var className = obj.bar.substr(obj.bar.lastIndexOf(".") + 1);
        return "net" + className + "Bar";
    },

    getPageTimingClass: function(timing)
    {
        return timing.classes ? timing.classes : "";
    },

    formatTime: function(time)
    {
        return Lib.formatTime(time);
    },

    formatStartTime: function(time)
    {
        var positive = time > 0;
        var label = Lib.formatTime(Math.abs(time));
        if (!time)
            return label;

        return (positive > 0 ? "+" : "-") + label;
    },

    getLabel: function(obj)
    {
        return Strings[obj.bar];
    },

    getTimingLabel: function(obj)
    {
        return obj.bar;
    },

    render: function(requestList, row, parentNode)
    {
        var input = requestList.input;
        var file = row.repObject;
        var page = HarModel.getParentPage(input, file);
        var pageStart = page ? Lib.parseISO8601(page.startedDateTime) : null;
        var requestStart = Lib.parseISO8601(file.startedDateTime);
        var infoTip = EntryTimeInfoTip.tableTag.replace({}, parentNode);

        // Insert start request time.
        var startTimeObj = {};

        //xxxHonza: the request start-time should be since the page start-time
        // but what to do if there was no parent page and the parent phase
        // is not the first one?
        //xxxHonza: the request start-time is since the page start-time
        // but the other case isw not tested yet.
        if (pageStart)
            startTimeObj.time = requestStart - pageStart;
        else
            startTimeObj.time = requestStart - row.phase.startTime;

        startTimeObj.bar = "request.Started";
        this.startTimeTag.insertRows({startTime: startTimeObj}, infoTip.firstChild);

        // Insert separator.
        this.separatorTag.insertRows({label: Strings["request.phases.label"]},
            infoTip.firstChild);

        var startTime = 0;
        var timings = [];

        // Helper shortcuts
        var blocked = file.timings.blocked;
        var dns = file.timings.dns;
        var ssl = file.timings.ssl; // new in HAR 1.2 xxxHonza: TODO
        var connect = file.timings.connect;
        var send = file.timings.send;
        var wait = file.timings.wait;
        var receive = file.timings.receive;

        if (blocked >= 0)
        {
            timings.push({bar: "request.phase.Blocking",
                elapsed: blocked,
                start: startTime});
        }

        if (dns >= 0)
        {
            timings.push({bar: "request.phase.Resolving",
                elapsed: dns,
                start: startTime += (blocked < 0) ? 0 : blocked});
        }

        if (connect >= 0)
        {
            timings.push({bar: "request.phase.Connecting",
                elapsed: connect,
                start: startTime += (dns < 0) ? 0 : dns});
        }

        if (send >= 0)
        {
            timings.push({bar: "request.phase.Sending",
                elapsed: send,
                start: startTime += (connect < 0) ? 0 : connect});
        }

        if (wait >= 0)
        {
            timings.push({bar: "request.phase.Waiting",
                elapsed: wait,
                start: startTime += (send < 0) ? 0 : send});
        }

        if (receive >= 0)
        {
            timings.push({bar: "request.phase.Receiving",
                elapsed: receive,
                start: startTime += (wait < 0) ? 0 : wait,
                loaded: file.loaded, fromCache: file.fromCache});
        }

        // Insert request timing info.
        this.timingsTag.insertRows({timings: timings}, infoTip.firstChild);

        if (!page)
            return true;

        // Get page event timing info (if the page exists).
        var events = [];
        for (var i=0; i<row.phase.pageTimings.length; i++)
        {
            var timing = row.phase.pageTimings[i];
            events.push({
                bar: timing.description ? timing.description : timing.name,
                start: pageStart + timing.time - requestStart,
                classes: timing.classes,
                time: timing.time
            });
        }

        if (events.length)
        {
            events.sort(function(a, b) {
                return (a.time < b.time) ? -1 : 1;
            });

            // Insert separator and timing info.
            this.separatorTag.insertRows({label: Strings["request.timings.label"]},
                infoTip.firstChild);
            this.eventsTag.insertRows({events: events}, infoTip.firstChild);
        }

        return true;
    }
});

// ********************************************************************************************* //

var EntrySizeInfoTip = domplate(
{
    tag:
        DIV({"class": "sizeInfoTip"}, "$file|getSize"),

    zippedTag:
        DIV(
            DIV({"class": "sizeInfoTip"}, "$file|getBodySize"),
            DIV({"class": "sizeInfoTip"}, "$file|getContentSize")
        ),

    getSize: function(file)
    {
        var bodySize = file.response.bodySize;
        if (bodySize < 0)
            return Strings.unknownSize;

        return Lib.formatString(Strings.tooltipSize,
            Lib.formatSize(bodySize),
            Lib.formatNumber(bodySize));
    },

    getBodySize: function(file)
    {
        var bodySize = file.response.bodySize;
        if (bodySize < 0)
            return Strings.unknownSize;

        return Lib.formatString(Strings.tooltipZippedSize,
            Lib.formatSize(bodySize),
            Lib.formatNumber(bodySize));
    },

    getContentSize: function(file)
    {
        var contentSize = file.response.content.size;
        if (contentSize < 0)
            return Strings.unknownSize;

        return Lib.formatString(Strings.tooltipUnzippedSize,
            Lib.formatSize(contentSize),
            Lib.formatNumber(contentSize));
    },

    render: function(requestList, row, parentNode)
    {
        var input = requestList.input;
        var file = row.repObject;
        if (file.response.bodySize == file.response.content.size)
            return this.tag.replace({file: file}, parentNode);

        return this.zippedTag.replace({file: file}, parentNode);
    }
});

// ********************************************************************************************* //

return RequestList;

// ********************************************************************************************* //
}});

/* See license.txt for terms of usage */

define(
'nls/pageList',{
    "root": {
        "column.label.url": "URL",
        "column.label.status": "Status",
        "column.label.type": "Type",
        "column.label.domain": "Domain",
        "column.label.size": "Size",
        "column.label.timeline": "Timeline",
        "action.label.Reset": "Reset"
    }
});


/* See license.txt for terms of usage */

define("preview/pageList", [
    "domplate/domplate",
    "core/lib",
    "core/trace",
    "core/cookies",
    "preview/requestList",
    "i18n!nls/pageList",
    "domplate/popupMenu"
],

function(Domplate, Lib, Trace, Cookies, RequestList, Strings, Menu) {
with (Domplate) {

// ********************************************************************************************* //
// Page List

function PageList(input)
{
    this.input = input;
    this.listeners = [];
}

/**
 * @domplate This object represents a template for list of pages.
 * This list is displayed within the Preview tab. 
 */
PageList.prototype = domplate(
/** @lends PageList */
{
    tableTag:
        TABLE({"class": "pageTable", cellpadding: 0, cellspacing: 0,
            onclick: "$onClick", _repObject: "$input"},
            TBODY(
                TAG("$rowTag", {groups: "$input.log.pages"})
            )
        ),

    rowTag:
        FOR("group", "$groups",
            TR({"class": "pageRow", _repObject: "$group"},
                TD({"class": "groupName pageCol", width: "1%"},
                    SPAN({"class": "pageName"}, "$group|getPageTitle")
                ),
                TD({"class": "netOptionsCol netCol", width: "15px"},
                    DIV({"class": "netOptionsLabel netLabel", onclick: "$onOpenOptions"})
                )
            )
        ),

    bodyTag:
        TR({"class": "pageInfoRow", style: "height:auto;"},
            TD({"class": "pageInfoCol", colspan: 2})
        ),

    // * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
    // Events & Callbacks

    getPageTitle: function(page)
    {
        return Lib.cropString(page.title, 100);
    },

    getPageID: function(page)
    {
        return "[" + page.id + "]";
    },

    onClick: function(event)
    {
        var e = Lib.fixEvent(event);
        if (Lib.isLeftClick(event)) 
        {
            var row = Lib.getAncestorByClass(e.target, "pageRow");
            if (row) 
            {
                this.toggleRow(row);
                Lib.cancelEvent(event);
            }
        }
    },

    toggleRow: function(row, forceOpen)
    {
        var opened = Lib.hasClass(row, "opened");
        if (opened && forceOpen)
            return;

        Lib.toggleClass(row, "opened");
        if (Lib.hasClass(row, "opened"))
        {
            var infoBodyRow = this.bodyTag.insertRows({}, row)[0];

            // Build request list for the expanded page.
            var requestList = this.createRequestList();

            // Dynamically append custom registered page timings.
            var pageTimings = PageList.prototype.pageTimings;
            for (var i=0; i<pageTimings.length; i++)
                requestList.addPageTiming(pageTimings[i]);

            requestList.render(infoBodyRow.firstChild, row.repObject);
        }
        else
        {
            var infoBodyRow = row.nextSibling;
            row.parentNode.removeChild(infoBodyRow);
        }
    },

    expandAll: function(pageList)
    {
        var row = pageList.firstChild.firstChild;
        while (row)
        {
            if (Lib.hasClass(row, "pageRow"))
                this.toggleRow(row, true);
            row = row.nextSibling;
        }
    },

    getPageRow: function(page)
    {
        var pageList = this.element.parentNode;
        var rows = Lib.getElementsByClass(pageList, "pageRow");
        for (var i=0; i<rows.length; i++)
        {
            var row = rows[i];
            if (row.repObject == page)
                return row;
        }
    },

    togglePage: function(page)
    {
        var row = this.getPageRow(page);
        this.toggleRow(row);
    },

    expandPage: function(page)
    {
        var row = this.getPageRow(page);
        this.toggleRow(row, true);
    },

    collapsePage: function(page)
    {
        var row = this.getPageRow(page);
        if (Lib.hasClass(row, "opened"))
            this.toggleRow(row);
    },

    // * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
    // Customize Columns

    onOpenOptions: function(event)
    {
        var e = Lib.fixEvent(event);
        Lib.cancelEvent(event);

        if (!Lib.isLeftClick(event))
            return;

        var target = e.target;

        // Collect all menu items.
        var row = Lib.getAncestorByClass(target, "pageRow");
        var items = this.getMenuItems(row.repObject);

        // Finally, display the the popup menu.
        // xxxHonza: the old <DIV> can be still visible.
        var menu = new Menu({id: "requestContextMenu", items: items});
        menu.showPopup(target);
    },

    getMenuItems: function(row)
    {
        // Get list of columns as string for quick search.
        var cols = RequestList.getVisibleColumns().join();

        // You can't hide the last visible column.
        var lastVisibleIndex;
        var visibleColCount = 0;

        var items = []
        for (var i=0; i<RequestList.columns.length; i++)
        {
            var colName = RequestList.columns[i];
            var visible = (cols.indexOf(colName) > -1);

            items.push({
                label: Strings["column.label." + colName],
                type: "checkbox",
                checked: visible,
                command: Lib.bindFixed(this.onToggleColumn, this, colName)
            });

            if (visible)
            {
                lastVisibleIndex = i;
                visibleColCount++;
            }
        }

        // If the last column is visible, disable its menu item.
        if (visibleColCount == 1)
            items[lastVisibleIndex].disabled = true;

        items.push("-");
        items.push({
            label: Strings["action.label.Reset"],
            command: Lib.bindFixed(this.updateColumns, this)
        });

        return items;
    },

    onToggleColumn: function(name)
    {
        // Try to remove the column from the array, if not presented append it.
        var cols = RequestList.getVisibleColumns();
        if (!Lib.remove(cols, name))
            cols.push(name);

        // Update Cookies and UI
        this.updateColumns(cols);
    },

    updateColumns: function(cols)
    {
        if (!cols)
            cols = RequestList.defaultColumns;

        RequestList.setVisibleColumns(cols);
    },

    // * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
    // Helpers 

    createRequestList: function()
    {
        var requestList = new RequestList(this.input);
        requestList.listeners = this.listeners;
        return requestList;
    },

    // * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
    // Public

    append: function(parentNode)
    {
        // According to the spec, network requests doesn't have to be 
        // associated with the parent page. This is to support even
        // tools that can't get this info.
        // Also if log files are merged there can be some requests not
        // associated with any page. Make sure these are displayed too. 
        var requestList = this.createRequestList();
        requestList.render(parentNode, null);

        // If there are any pages, build regular page list.
        var pages = this.input.log.pages;
        if (pages && pages.length)
        {
            // Build the page list.
            var table = this.tableTag.append({input: this.input}, parentNode, this);

            // List of pages within one HAR log
            var pageRows = Lib.getElementsByClass(table, "pageRow");

            // List of HAR logs
            var pageTables = Lib.getElementsByClass(parentNode, "pageTable");

            // Expand appended page by default only if there is only one page.
            // Note that there can be more page-lists (pageTable elements)
            if (pageRows.length == 1 && pageTables.length == 1)
                this.toggleRow(pageRows[0]);

            // If 'expand' parameter is specified expand all by default.
            var expand = Lib.getURLParameter("expand");
            if (expand)
                this.expandAll(table);
        }
    },

    render: function(parentNode)
    {
        this.append(parentNode);
    },

    // * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
    // Listeners

    addListener: function(listener)
    {
        this.listeners.push(listener);
    },

    removeListener: function(listener)
    {
        Lib.remove(this.listeners, listener);
    }
});

// ********************************************************************************************* //

// Custom registered page timings, displayed as vertical lines over individual requests
// in the first phase.
PageList.prototype.pageTimings = [];

// ********************************************************************************************* //

return PageList;

// ********************************************************************************************* //
}});

/* See license.txt for terms of usage */

define("preview/validationError", [
    "domplate/domplate",
    "core/lib",
    "core/trace",
    "domplate/popupMenu"
],

function(Domplate, Lib, Trace, Menu) { with (Domplate) {

// ********************************************************************************************* //
// Template for displaying validation errors

var ValidationError = domplate(
{
    // Used in case of parsing or validation errors.
    errorTable:
        TABLE({"class": "errorTable", cellpadding: 3, cellspacing: 0},
            TBODY(
                FOR("error", "$errors",
                    TR({"class": "errorRow", _repObject: "$error"},
                        TD({"class": "errorProperty"},
                            SPAN("$error.property")
                        ),
                        TD({"class": "errorOptions", $hasTarget: "$error|hasTarget"},
                            DIV({"class": "errorOptionsTarget", onclick: "$onOpenOptions"},
                                "&nbsp;"
                            )
                        ),
                        TD("&nbsp;"),
                        TD({"class": "errorMessage"},
                            SPAN("$error.message"
                            )
                        )
                    )
                )
            )
        ),

    hasTarget: function(error)
    {
        return error.input && error.file;
    },

    // * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
    // Events

    onOpenOptions: function(event)
    {
        var e = Lib.fixEvent(event);
        Lib.cancelEvent(event);

        if (!Lib.isLeftClick(event))
            return;

        var target = e.target;

        // Collect all menu items.
        var row = Lib.getAncestorByClass(target, "errorRow");
        var error = row.repObject;
        if (!error || !error.input || !error.file)
            return;

        var items = this.getMenuItems(error.input, error.file);
        if (!items.length)
            return;

        // Finally, display the the popup menu.
        // xxxHonza: the old <DIV> can be still visible.
        var menu = new Menu({id: "requestContextMenu", items: items});
        menu.showPopup(target);
    },

    getMenuItems: function(input, file)
    {
        var items = [];
        Lib.dispatch(this.listeners, "getMenuItems", [items, input, file]);
        return items;
    },

    // * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
    // Listeners

    listeners: [],

    addListener: function(listener)
    {
        this.listeners.push(listener);
    },

    removeListener: function(listener)
    {
        Lib.remove(this.listeners, listener);
    },

    // * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
    // Tab

    appendError: function(err, parentNode)
    {
        if (err.errors)
            this.errorTable.append(err, parentNode);
    }
});

// ********************************************************************************************* //

return ValidationError;

// ********************************************************************************************* //
}});

/* See license.txt for terms of usage */

/**
 * @module tabs/previewTab
 */
define("tabs/previewTab", [
    "domplate/domplate",
    "domplate/tabView",
    "core/lib",
    "i18n!nls/previewTab",
    "domplate/toolbar",
    "tabs/pageTimeline",
    "tabs/pageStats",
    "preview/pageList",
    "core/cookies",
    "preview/validationError"
],

function(Domplate, TabView, Lib, Strings, Toolbar, Timeline, Stats, PageList, Cookies,
    ValidationError) {

with (Domplate) {

//*************************************************************************************************
// Home Tab

function PreviewTab(model)
{
    this.model = model;

    this.toolbar = new Toolbar();
    this.timeline = new Timeline();
    this.stats = new Stats(model, this.timeline);

    // Initialize toolbar.
    this.toolbar.addButtons(this.getToolbarButtons());

    // Context menu listener.
    ValidationError.addListener(this);
}

PreviewTab.prototype = Lib.extend(TabView.Tab.prototype,
{
    id: "Preview",
    label: Strings.previewTabLabel,

    // Use tabBodyTag so, the basic content layout is rendered immediately
    // and not as soon as the tab is actually selected. This is useful when
    // new data are appended while the tab hasn't been selected yet.
    tabBodyTag:
        DIV({"class": "tab$tab.id\\Body tabBody", _repObject: "$tab"},
            DIV({"class": "previewToolbar"}),
            DIV({"class": "previewTimeline"}),
            DIV({"class": "previewStats"}),
            DIV({"class": "previewList"})
        ),

    // * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
    // Tab

    onUpdateBody: function(tabView, body)
    {
        // Render all UI components except of the page list. The page list is rendered
        // as soon as HAR data are loaded into the page.
        this.toolbar.render(Lib.$(body, "previewToolbar"));
        this.stats.render(Lib.$(body, "previewStats"));
        this.timeline.render(Lib.$(body, "previewTimeline"));

        // Show timeline & stats by default if the cookie says so (no animation)
        // But there should be an input.
        var input = this.model.input;
        if (input && Cookies.getCookie("timeline") == "true")
            this.onTimeline(false);

        if (input && Cookies.getCookie("stats") == "true")
            this.onStats(false);
    },

    getToolbarButtons: function()
    {
        var buttons = [
            {
                id: "showTimeline",
                label: Strings.showTimelineButton,
                tooltiptext: Strings.showTimelineTooltip,
                command: Lib.bindFixed(this.onTimeline, this, true)
            },
            {
                id: "showStats",
                label: Strings.showStatsButton,
                tooltiptext: Strings.showStatsTooltip,
                command: Lib.bindFixed(this.onStats, this, true)
            },
            {
                id: "printPage",
                label: Strings.printPageButton,
                command: Lib.bindFixed(this.onPrintPage, this)
            }/*,
            {
                id: "clear",
                label: Strings.clearButton,
                tooltiptext: Strings.clearTooltip,
                command: Lib.bindFixed(this.onClear, this)
            }*/
        ];

        buttons.push({
            id: "download",
            tooltiptext: Strings.downloadTooltip,
            className: "harDownloadButton"
        });

        return buttons;
    },

    // * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
    // Toolbar commands

    onTimeline: function(animation)
    {
        // Update showTimeline button label.
        var button = this.toolbar.getButton("showTimeline");
        if (!button)
            return;

        this.timeline.toggle(animation);

        var visible = this.timeline.isVisible();
        button.label = Strings[visible ? "hideTimelineButton" : "showTimelineButton"];

        // Re-render toolbar to update label.
        this.toolbar.render();

        Cookies.setCookie("timeline", visible);
    },

    onPrintPage: function()
    {
        window.print();
    },

    onStats: function(animation)
    {
        // Update showStats button label.
        var button = this.toolbar.getButton("showStats");
        if (!button)
            return;

        this.stats.toggle(animation);

        var visible = this.stats.isVisible();
        button.label = Strings[visible ? "hideStatsButton" : "showStatsButton"];

        // Re-render toolbar to update label.
        this.toolbar.render();

        Cookies.setCookie("stats", visible);
    },

    onClear: function()
    {
        this.stats.cleanUp();
    },

    // * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
    // Public

    showStats: function(show)
    {
        Cookies.setCookie("stats", show);
    },

    showTimeline: function(show)
    {
        Cookies.setCookie("timeline", show);
    },

    append: function(input)
    {
        // The page list is responsible for rendering expandable list of pages and requests.
        // xxxHonza: There should probable be a list of all pageLists. Inside the pageList?
        var pageList = new PageList(input);
        pageList.append(Lib.$(this._body, "previewList"));

        // Append new pages into the timeline.
        this.timeline.append(input);

        // Register context menu listener (provids additional commands for the context menu).
        pageList.addListener(this);
    },

    appendError: function(err)
    {
        ValidationError.appendError(err, Lib.$(this._body, "previewList"));
    },

    addPageTiming: function(timing)
    {
        PageList.prototype.pageTimings.push(timing);
    },

    // * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
    // Request List Commands

    getMenuItems: function(items, input, file)
    {
        if (!file)
            return;

        items.push("-");
        items.push(
        {
            label: Strings.menuShowHARSource,
            command: Lib.bind(this.showHARSource, this, input, file)
        });
    },

    showHARSource: function(menu, input, file)
    {
        var domTab = this.tabView.getTab("DOM");
        if (!domTab)
            return;

        domTab.select("DOM");
        domTab.highlightFile(input, file);
    }
});

//*************************************************************************************************

return PreviewTab;

//*************************************************************************************************
}});

/* See license.txt for terms of usage */

/**
 * @module harViewer
 */
define("harViewer", [
    "domplate/tabView",
    "tabs/homeTab",
    "tabs/aboutTab",
    "tabs/previewTab",
    "preview/harModel",
    "i18n!nls/harViewer",
    "preview/requestList",
    "core/lib",
    "core/trace"
],

function(TabView, HomeTab, AboutTab, PreviewTab, HarModel,
    Strings, RequestList, Lib, Trace) {

// ********************************************************************************************* //
// The Application

function HarView()
{
    this.id = "harView";

    // Location of the model (all tabs see its parent and so the model).
    this.model = new HarModel();

    // Append tabs
    this.appendTab(new HomeTab());
    this.appendTab(new PreviewTab(this.model));
    this.appendTab(new AboutTab());
}

/**
 * This is the Application UI configuration code. The Viewer UI is based on a Tabbed UI
 * interface and is composed from following tabs:
 *
 * {@link HomeTab}: This is the starting application tab. This tab allows direct inserting of
 *      a HAR log source to preview. There are also some useful links to existing example logs.
 *      This page is displyed by default unless there is a HAR file specified in the URL.
 *      In such case the file is automatically loaded and {@link PreviewTab} selected.
 *
 * {@link PreviewTab}: This tab is used to preview one or more HAR files. The UI is composed
 *      from an expandable list of pages and requests. There is also a graphical timeline
 *      that shows request timings.
 *
 * {@link AboutTab}: Shows some basic information about the HAR Viewer and links to other
 *      resources.
 */
HarView.prototype = Lib.extend(new TabView(),
/** @lends HarView */
{
    initialize: function(content)
    {
        // Global application properties.
        this.version = content.getAttribute("version");
        this.harSpecURL = "http://www.softwareishard.com/blog/har-12-spec/";

        this.render(content);
        this.selectTabByName("Home");

        // Auto load all HAR files specified in the URL.
        var okCallback = Lib.bind(this.appendPreview, this);
        var errorCallback = Lib.bind(this.onLoadError, this);

        if (HarModel.Loader.run(okCallback, errorCallback))
        {
            var homeTab = this.getTab("Home");
            if (homeTab)
                homeTab.loadInProgress(true);
        }
    },

    appendPreview: function(jsonString)
    {
        var homeTab = this.getTab("Home");
        var previewTab = this.getTab("Preview");

        try
        {
            var validate = $("#validate").prop("checked");
            var input = HarModel.parse(jsonString, validate);
            this.model.append(input);

            if (previewTab)
            {
                // xxxHonza: this should be smarter.
                // Make sure the tab is rendered now.
                try
                {
                    previewTab.select();
                    previewTab.append(input);
                }
                catch (err)
                {
                    Trace.exception("HarView.appendPreview; EXCEPTION ", err);
                    if (err.errors && previewTab)
                        previewTab.appendError(err);
                }
            }

        }
        catch (err)
        {
            Trace.exception("HarView.appendPreview; EXCEPTION ", err);
            if (err.errors && previewTab)
                previewTab.appendError(err);
        }

        // Select the preview tab in any case.
        previewTab.select();

        // HAR loaded, parsed and appended into the UI, let's shut down the progress.
        if (homeTab)
            homeTab.loadInProgress(false);

        Lib.fireEvent(content, "onViewerHARLoaded");
    },

    onLoadError: function(jqXHR, textStatus, errorThrown)
    {
        var homeTab = this.getTab("Home");
        if (homeTab)
            homeTab.loadInProgress(true, jqXHR.statusText);

        Trace.error("harModule.loadRemoteArchive; ERROR ", jqXHR, textStatus, errorThrown);
    },

    // * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
    // Loading HAR files

    /**
     * Load HAR file
     * @param {String} url URL of the target log file
     * @param {Object} settings A set of key/value pairs taht configure the request.
     *      All settings are optional.
     *      settings.jsonp {Boolean} If you wish to force a crossDomain request using JSONP,
     *          set the value to true. You need to use HARP syntax for the target file.
     *          Default is false.
     *      settings.jsonpCallback {String} Override the callback function name used in HARP.
     *          Default is "onInputData".
     *      settings.success {Function} A function to be called when the file is successfully
     *          loaded. The HAR object is passed as an argument.
     *      settings.ajaxError {Function} A function to be called if the AJAX request fails.
     *          An error object is pased as an argument.
     */
    loadHar: function(url, settings)
    {
        settings = settings || {};
        return HarModel.Loader.load(this, url,
            settings.jsonp,
            settings.jsonpCallback,
            settings.success,
            settings.ajaxError);
    },

    /**
     * Use to customize list of request columns displayed by default.
     *
     * @param {String} cols Column names separated by a space.
     * @param {Boolean} avoidCookies Set to true if you don't want to touch cookies.
     */
    setPreviewColumns: function(cols, avoidCookies)
    {
        RequestList.setVisibleColumns(cols, avoidCookies);
    }
});

// ********************************************************************************************* //
// Initialization

var content = document.getElementById("content");
var harView = content.repObject = new HarView();

// Fire some events for listeners. This is useful for extending/customizing the viewer.
Lib.fireEvent(content, "onViewerPreInit");
harView.initialize(content);
Lib.fireEvent(content, "onViewerInit");

Trace.log("HarViewer; initialized OK");

// ********************************************************************************************* //
});

