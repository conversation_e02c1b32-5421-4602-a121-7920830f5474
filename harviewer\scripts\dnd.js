/*
Copyright 2012 Google Inc.
Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at
     http://www.apache.org/licenses/LICENSE-2.0
Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
Author: <PERSON> (<EMAIL>)

Modifications made by: <PERSON>
*/
define("dnd", function () {
    return function DnDFileController(selector, onDropCallback, enterCallback, leaveCallback) {
        var el_ = document.querySelector(selector);
        var overCount = 0;

        this.dragenter = function (e) {
            e.stopPropagation();
            e.preventDefault();
            overCount++;
            el_.classList.add('dropping');
            if (typeof enterCallback === 'function') {
                enterCallback(e);
            }
        };

        this.dragover = function (e) {
            e.stopPropagation();
            e.preventDefault();
        };

        this.dragleave = function (e) {
            e.stopPropagation();
            e.preventDefault();
            if (--overCount <= 0) {
                el_.classList.remove('dropping');
                overCount = 0;
                if (typeof leaveCallback === 'function') {
                    leaveCallback(e);
                }
            }
        };

        this.drop = function (e) {
            e.stopPropagation();
            e.preventDefault();

            overCount = 0;
            el_.classList.remove('dropping');
            if (typeof leaveCallback === 'function') {
                leaveCallback(e);
            }

            onDropCallback(e.dataTransfer);
        };

        el_.addEventListener('dragenter', this.dragenter, false);
        el_.addEventListener('dragover', this.dragover, false);
        el_.addEventListener('dragleave', this.dragleave, false);
        el_.addEventListener('drop', this.drop, false);
    }
});
