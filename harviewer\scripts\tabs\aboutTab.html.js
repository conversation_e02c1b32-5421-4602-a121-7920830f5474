define('text!tabs/aboutTab.html',[],function () { return '<div>\r\n<h2>HTTP Archive Viewer</h2>\r\n<table style="width:600px;">\r\n<tr><td>\r\n<p>The purpose of this online tool is to visualize\r\n<a href="http://www.softwareishard.com/blog/har-12-spec/">\r\n    HTTP Archive (HAR)</a>\r\nlog files created by HTTP tracking tools. These files contain log of HTTP\r\nclient/server conversation and can be used for an additional analysis of e.g. \r\npage load performance.</p>\r\n\r\n<p>User interface of this tool is composed from the following tabs:</p>\r\n<ul>\r\n<li><b>Load</b> - Paste content of a log file into the text box in this tab.</li>\r\n<li><b>Inspect</b> - Switch to this tab if you want to see visualised HTTP traffic.</li>\r\n</ul>\r\n</td></tr>\r\n\r\n<tr><td>\r\n<h3>HTTP Archive Specification</h3>\r\n<p>Required\r\n<a href="http://www.softwareishard.com/blog/har-12-spec/">\r\nstructure</a> of the input HTTP Archive file (*.har) is described using\r\n<a href="http://www.json.com/json-schema-proposal/">JSON Schema</a>.\r\nYou can explore the current schema definition within the <span class="linkSchema link">Schema</span>\r\ntab on this page.</p>\r\n</td></tr>\r\n\r\n<tr><td>\r\n<h3>Request Timing Fields</h3>\r\n<p>Part of the HTTP log is also a timing info about network request executions.\r\nHere is a description of individual request/response phases:</p>\r\n<ul>\r\n<li><i>Blocking</i> - Time spent in a queue waiting for a network connection.</li>\r\n<li><i>DNS Lookup</i> - DNS resolution time. The time required to resolve a host name.</li>\r\n<li><i>Connecting</i> - Time required to create TCP connection.</li>\r\n<li><i>Sending</i> - Time required to send HTTP request to the server.</li>\r\n<li><i>Waiting</i> - Waiting for a response from the server.</li>\r\n<li><i>Receiving</i> - Time required to read entire response from the server (or cache).</li>\r\n</ul>\r\n</td></tr>\r\n\r\n<tr><td>\r\n<h3>Online Log Files</h3>\r\n<p>HAR Viewer also supports JSONP and so, it\'s possible to load log files \r\nfrom different domains. This allows linking your online logs and preview them\r\nautomatically within the viewer. See live\r\n<a href="?inputUrl=http://www.janodvarko.cz/har/viewer/examples/inline-scripts-block.harp">example</a>.\r\n</p>\r\n\r\n<p><i>1. The Content of a *.har file must be enclosed within a callback function:</i></p>\r\n<code>onInputData({ "log": { ... } })</code>\r\n\r\n<p><i>2. The link displaying a *.har file (using this viewer) must specify URL of\r\nthe file in <b>inputUrl</b> parameter:</i></p>\r\n<code>http://www.softwareishard.com/har/viewer/?inputUrl=http://www.example.com/netData.har</code>\r\n\r\n<p><i>3. A custom name of the callback function can be specified in a <b>callback</b> parameter\r\n(by default it\'s <b>onInputData</b>):</i></p>\r\n<code>http://www.softwareishard.com/har/viewer/?inputUrl=http://www.example.com/netData.har&amp;callback=onInputData</code>\r\n<br/><br/>\r\n</td></tr>\r\n\r\n</table>\r\n\r\n<br/><br/>\r\n<i>HAR Viewer author: Jan Odvarko, <EMAIL></i>\r\n</div>\r\n';});
