/* See license.txt for terms of usage */

define(
{
    "root": {
        "validationType": "HAR Validation",
        "validationSumTimeError": "Sum of request timings doesn't correspond to the total value: " +
            "%S (request.time: %S vs. sum: %S), request#: %S, parent page: %S",
        "validationNegativeTimeError": "Negative time is not allowed: " +
            "%S, request#: %S, parent page: %S"
    }
});
