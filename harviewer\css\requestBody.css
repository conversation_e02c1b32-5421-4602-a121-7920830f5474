/* See license.txt for terms of usage */ .requestBodyBodies{border-left:1px solid #D7D7D7;border-right:1px solid #D7D7D7;border-bottom:1px solid #D7D7D7;}.netInfoRow .tabView{width:99%;}.netInfoText{padding:8px;background-color:#FFFFFF;font-family:Monaco,monospace;}.netInfoText[selected="true"]{display:block;}.netInfoParamName{padding:0 10px 0 0;font-family:Lucida Grande,Tahoma,sans-serif;font-weight:bold;vertical-align:top;text-align:right;white-space:nowrap;}.netInfoParamValue > PRE{margin:0}.netInfoHeadersText,.netInfoCookiesText{padding-top:0;width:100%;}.netInfoParamValue{width:100%;}.netInfoHeadersGroup,.netInfoCookiesGroup{margin-bottom:4px;border-bottom:1px solid #D7D7D7;padding-top:8px;padding-bottom:2px;font-family:Lucida Grande,Tahoma,sans-serif;font-weight:bold;color:#565656;}.netInfoHtmlPreview{border:0;width:100%;height:100px;}.netInfoHtmlText{padding:0;}.htmlPreviewResizer{width:100%;height:4px;background-image:url(images/splitterh.png);background-repeat:repeat-x;cursor:s-resize;}body[hResizing="true"] .netInfoHtmlPreview{pointer-events:none !important;}