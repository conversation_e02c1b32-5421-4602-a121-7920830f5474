/* See license.txt for terms of usage */

define(
{
    "root": {
        "domTabLabel": "Structure",
        "searchDisabledForIE": "You need Mozilla or WebKit based browser to search in HAR",
        "searchOptionJsonQuery": "JSON Query",
        "tableView": "Table View",
        "searchResultsDefaultText": "JSON Query Results",
        "searchPlaceholder": "Search",
        "jsonQueryPlaceholder": "JSON Query",
        "queryResultsTableView": "Table View"
    }
});
