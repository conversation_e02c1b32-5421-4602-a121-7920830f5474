/* See license.txt for terms of usage */ /* See license.txt for terms of usage */ body{-webkit-user-select:none;}.harBody{margin:0;font-family:Lucida Grande,Tahoma,sans-serif;font-size:13px;}#content a{text-decoration:none;}#content h1{font-size:17px;border-bottom:1px solid threedlightshadow;}#content h2{color:#DD467B;font-size:22.8px;font-weight:lighter;}#content h3{color:#DD467B;font-weight:bold;}#content pre{margin:0;font:inherit;}.collapsed,[collapsed="true"]{display:none !important;}.link{color:blue;}.link:hover{cursor:pointer;}.harViewBodies{position:absolute;top:33px;bottom:0px;}.harViewBar > .tab{font-size:17px;font-family:Lucida Grande,Tahoma,sans-serif;}.harView[hideTabBar="true"] .harViewBar{display:none;}.harView[hideTabBar="true"] .harViewBodies{position:inherit;}/* See license.txt for terms of usage */ .tabView{width:100%;background-color:#FFFFFF;color:#000000;}.tabViewCol{background:url(images/timeline-sprites.png) repeat-x scroll 0 -112px #FFFFFF;vertical-align:top;}.tabViewBody{margin:2px 0px 0px 0px;}.tabBar{padding-left:14px;border-bottom:1px solid #D7D7D7;white-space:nowrap;}.tab{position:relative;top:1px;padding:4px 8px;border:1px solid transparent;border-bottom:none;color:#565656;font-weight:bold;white-space:nowrap;-moz-user-select:none;display:inline-block;}.tab:hover{cursor:pointer;border-color:#D7D7D7;-moz-border-radius:4px 4px 0 0;-webkit-border-top-left-radius:4px;-webkit-border-top-right-radius:4px;border-radius:4px 4px 0 0;}.tab[selected="true"],.tab .selected{cursor:default !important;border-color:#D7D7D7;background-color:#FFFFFF;-moz-border-radius:4px 4px 0 0;-webkit-border-top-left-radius:4px;-webkit-border-top-right-radius:4px;border-radius:4px 4px 0 0;}.tabBodies{width:100%;overflow:auto;}.tabBody{display:none;margin:0;}.tabBody[selected="true"],.tabBody.selected{display:block;}@media print{.tabBodies{overflow:visible;}.tabViewCol{background:none;}}/* See license.txt for terms of usage */ .toolbar{font-family:Verdana,Geneva,Arial,Helvetica,sans-serif;font-size:11px;font-weight:400;font-style:normal;border-bottom:1px solid #EEEEEE;padding:0 3px 0 3px;-webkit-user-select:none;}.toolbarButton,.toolbarSeparator{display:inline-block;vertical-align:middle;cursor:pointer;color:#000000;-moz-user-select:none;-moz-box-sizing:padding-box;}.toolbarButton.dropDown .arrow{width:11px;height:10px;background:url(images/contextMenuTarget.png) no-repeat;display:inline-block;margin-left:3px;position:relative;right:0;top:1px;}.toolbarButton.image{padding:0;height:16px;width:16px;}.toolbarButton.text,.toolbarSeparator{margin:3px 0 3px 0;padding:3px;border:1px solid transparent;}.toolbarButton.text:hover{background:url(images/bg-button.gif) repeat-x scroll 0 0 #FFFFFF;border-top:1px solid #bbb;border-bottom:1px solid #aaa;border-left:1px solid #bbb;border-right:1px solid #aaa;-moz-border-radius:3px;-webkit-border-radius:3px;border-radius:3px;}.toolbarButton.text:active{background-position:0 -400px;}/* See license.txt for terms of usage */ .domTable{}.memberLabelCell{padding:2px 50px 2px 0;vertical-align:top;}.memberValueCell{padding:1px 0 1px 5px;display:block;overflow:hidden;}.memberLabel{cursor:default;-moz-user-select:none;overflow:hidden;padding-left:18px;white-space:nowrap;}.memberRow.hasChildren.opened > .memberLabelCell > .memberLabel{background-image:url(images/twisty-sprites.png);background-position:3px -16px;background-color:transparent;}.memberRow.hasChildren > .memberLabelCell > .memberLabel:hover{cursor:pointer;color:blue;text-decoration:underline;}.memberRow.hasChildren > .memberLabelCell > .memberLabel{background-image:url(images/twisty-sprites.png);background-repeat:no-repeat;background-position:3px 3px;}.jumpHighlight{background-color:#C4F4FF !important;}.objectBox-object{color:gray;}.objectBox-number{color:#000088;}.objectBox-string{color:#FF0000;white-space:pre-wrap;}.objectBox-null,.objectBox-undefined{font-style:italic;color:#787878;}.objectBox-array{color:gray;}/* See license.txt for terms of usage */ .infoTip{z-index:**********;position:fixed;padding:2px 4px 3px 4px;background:LightYellow;font-family:Lucida Grande,Tahoma,sans-serif;color:#000000;display:none;white-space:nowrap;font-size:11px;border:1px solid rgb(126,171,205);background:url(images/tabEnabled.png) repeat-x scroll 0px 0px rgb(249,249,249);background-position-x:0;background-position-y:100%;background-repeat:repeat-x;-moz-border-radius:3px;-webkit-border-radius:3px;border-radius:3px;-moz-box-shadow:gray 2px 2px 3px;-webkit-box-shadow:gray 2px 2px 3px;box-shadow:gray 2px 2px 3px;filter:progid:DXImageTransform.Microsoft.dropshadow(OffX=2,OffY=2,Color='gray');-ms-filter:"progid:DXImageTransform.Microsoft.dropshadow(OffX=2,OffY=2,Color='gray')";}.infoTip[active="true"]{display:block;}.infoTip[multiline="true"]{background-image:none;}/* See license.txt for terms of usage */ .popupMenu{display:none;position:absolute;font-size:11px;z-index:**********;font-family:Lucida Grande,Tahoma,sans-serif;}.popupMenuContent{padding:2px;}.popupMenuSeparator{display:block;position:relative;padding:1px 18px 0;text-decoration:none;color:#000;cursor:default;background:#ACA899;margin:2px 0;}.popupMenuOption{display:block;position:relative;padding:2px 18px;text-decoration:none;color:#000;cursor:default;}.popupMenuOption:hover{color:#fff;background:#316AC5;}.popupMenuGroup{background:transparent url(images/menu/tabMenuPin.png) no-repeat right 0;}.popupMenuGroup:hover{background:#316AC5 url(images/menu/tabMenuPin.png) no-repeat right -17px;}.popupMenuGroupSelected{color:#fff;background:#316AC5 url(images/menu/tabMenuPin.png) no-repeat right -17px;}.popupMenuChecked{background:transparent url(images/menu/tabMenuCheckbox.png) no-repeat 4px 0;}.popupMenuChecked:hover{background:#316AC5 url(images/menu/tabMenuCheckbox.png) no-repeat 4px -17px;}.popupMenuRadioSelected{background:transparent url(images/menu/tabMenuRadio.png) no-repeat 4px 0;}.popupMenuRadioSelected:hover{background:#316AC5 url(images/menu/tabMenuRadio.png) no-repeat 4px -17px;}.popupMenuShortcut{padding-right:85px;}.popupMenuShortcutKey{position:absolute;right:0;top:2px;width:77px;}.popupMenuDisabled{color:#ACA899 !important;}.popupMenuShadow{float:left;background:url(images/menu/shadowAlpha.png) no-repeat bottom right !important;margin:10px 0 0 10px !important;margin:10px 0 0 5px;}.popupMenuShadowContent{display:block;position:relative;background-color:#fff;border:1px solid #a9a9a9;top:-6px;left:-6px;}#optionsMenu{top:22px;left:0;}/* See license.txt for terms of usage */ .dataTableSizer{margin:7px;border:1px solid #EEEEEE;}.dataTableSizer:focus{outline:none;}.dataTable{}.logGroup .dataTable{border:none;}.dataTable TBODY{overflow-x:hidden;overflow-y:scroll;}.dataTable > .dataTableTbody > tr:nth-child(even){background-color:#EFEFEF;}.dataTable a{vertical-align:middle;}.dataTableTbody > tr > td{padding:1px 4px 0 4px;}.dataTableTbody > tr > td:last-child{padding-right:20px;}.useA11y .dataTable *:focus{outline-offset:-2px;}.panelNode.hideType-table .logRow-table{display:none !important;}.headerCell{cursor:pointer;-moz-user-select:none;border-bottom:1px solid #9C9C9C;padding:0 !important;font-weight:bold;background:#C8C8C8 -moz-linear-gradient(top,rgba(255,255,255,0.3),rgba(0,0,0,0.2));background:#C8C8C8 -webkit-gradient(linear,left top,left bottom,from(rgba(255,255,255,0.3)),to(rgba(0,0,0,0.2)));}.headerCellBox{padding:2px 13px 2px 4px;border-left:1px solid #D9D9D9;border-right:1px solid #9C9C9C;white-space:nowrap;}.headerCell:hover:active{background-color:#B4B4B4;}.headerSorted{background-color:#8CA0BE;}.headerSorted > .headerCellBox{border-right-color:#6B7C93;background:url(chrome://firebug/skin/arrowDown.png) no-repeat right;}.headerSorted.sortedAscending > .headerCellBox{background-image:url(chrome://firebug/skin/arrowUp.png);}.headerSorted:hover:active{background-color:#6E87AA;}.memberRow.tableCellRow .memberLabelCell,.memberRow.tableCellRow .memberValueCell{padding:0;color:Gray;}.dataTableCell > .objectBox-number,.dataTableCell > .objectBox-string,.dataTableCell > .objectBox-null,.dataTableCell > .objectBox-undefined,.dataTableCell > .objectBox-array{padding:5px;}/* See license.txt for terms of usage */ .pageList{width:100%;}.pageTable{width:100%;font-family:Lucida Grande,Tahoma,sans-serif;font-size:11px;}.pageCol{white-space:nowrap;border-bottom:1px solid #EEEEEE;}.pageRow{font-weight:bold;height:17px;background-color:white;}.pageRow:hover{background:#EFEFEF;}.opened > .pageCol > .pageName{background-image:url(images/twisty-sprites.png);background-position:3px -17px;}.pageName{background-image:url(images/twisty-sprites.png);background-repeat:no-repeat;background-position:3px 2px;padding-left:18px;font-weight:bold;cursor:pointer;}.pageID{color:gray;}.pageInfoCol{background:url(images/timeline-sprites.png) repeat-x scroll 0 -112px #FFFFFF;padding:0px 0px 4px 17px;}.pageRow:hover > .netOptionsCol > .netOptionsLabel{display:block;}.pageRow > .netOptionsCol{padding-right:2px;}@media print{.pageInfoCol{background:none;}}/* See license.txt for terms of usage */ .netTable{width:100%;border-left:1px solid #EFEFEF;font-family:Lucida Grande,Tahoma,sans-serif;font-size:11px;table-layout:fixed;}.netRow{background:white;}.netRow.loaded{background:#FFFFFF;}.netHrefCol:hover{}.netRow.loaded:hover{background:#EFEFEF;}.netCol{padding:0;vertical-align:top;border-bottom:1px solid #EFEFEF;white-space:nowrap;text-overflow:clip;overflow:hidden;}.netRow[breakLayout="true"] .netCol{border-top:1px solid rgb(207,207,207);}.netTypeCol,.netStatusCol{color:rgb(128,128,128);}.responseError > .netStatusCol{color:red;}.responseRedirect > td{color:#f93;}.netStatusCol,.netTypeCol,.netDomainCol,.netSizeCol,.netTimeCol{padding-left:8px;}.netTimeCol{overflow:visible;}.netSizeCol{text-align:right;}.netHrefLabel{-moz-box-sizing:padding-box;overflow:hidden;z-index:100;position:relative;padding-left:18px;padding-top:1px;font-weight:bold;}.netFullHrefLabel{position:absolute;display:none;-moz-user-select:none;padding-right:10px;padding-bottom:3px;background:#FFFFFF;}.netHrefCol:hover > .netStatusLabel,.netHrefCol:hover > .netDomainLabel,.netHrefCol:hover > .netHrefLabel{display:none;}.netHrefCol:hover > .netFullHrefLabel{display:block;}.netRow.loaded:hover > .netCol > .netFullHrefLabel{background-color:#EFEFEF;}.netStatusLabel,.netTypeLabel,.netDomainLabel,.netSizeLabel,.netTimelineBar{padding:1px 0 2px 0 !important;}.responseError{color:red;}.netOptionsCol{padding-left:2px;padding-top:3px;}.netOptionsLabel{width:11px;height:10px;background:url(images/contextMenuTarget.png) no-repeat;display:none;}.netRow:hover > .netOptionsCol > .netOptionsLabel{display:block;}.netOptionsLabel:hover{background-image:url(images/contextMenuTargetHover.png);}.netHrefLabel:hover{cursor:pointer;}.isExpandable .netHrefLabel:hover{cursor:pointer;color:blue;text-decoration:underline;}.netTimelineBar{position:relative;border-right:50px solid transparent;}.netBlockingBar{position:absolute;left:0;top:0;bottom:0;background:#FFFFFF url(images/timeline-sprites.png) repeat-x;min-width:0px;z-index:70;height:16px;}.netResolvingBar{position:absolute;left:0;top:0;bottom:0;background:url(images/timeline-sprites.png) repeat-x scroll 0 -16px #FFFFFF;min-width:0px;z-index:60;height:16px;}.netConnectingBar{position:absolute;left:0;top:0;bottom:0;background:url(images/timeline-sprites.png) repeat-x scroll 0 -32px #FFFFFF;min-width:0px;z-index:50;height:16px;}.netSendingBar{position:absolute;left:0;top:0;bottom:0;background:url(images/timeline-sprites.png) repeat-x scroll 0 -48px #FFFFFF;min-width:0px;z-index:40;height:16px;}.netWaitingBar{position:absolute;left:0;top:0;bottom:0;background:url(images/timeline-sprites.png) repeat-x scroll 0 -64px #FFFFFF;min-width:1px;z-index:30;height:16px;}.netReceivingBar{position:absolute;left:0;top:0;bottom:0;background:url(images/timeline-sprites.png) repeat-x scroll 0 -80px #B6B6B6;min-width:0px;z-index:20;height:16px;}.fromCache .netReceivingBar,.fromCache.netReceivingBar{background:url(images/timeline-sprites.png) repeat-x scroll 0 -96px #D6D6D6;border-color:#D6D6D6;height:16px;}.netPageTimingBar{position:absolute;left:0;top:0;bottom:0;width:1px;z-index:90;opacity:0.5;display:none;background-color:green;margin-bottom:-1px;border-left:1px solid white;border-right:1px solid white;}.netWindowLoadBar{background-color:red;}.netContentLoadBar{background-color:blue;}.netTimeStampBar{background-color:olive;}.netTimeLabel{-moz-box-sizing:padding-box;position:absolute;top:1px;left:100%;padding-left:6px;color:#444444;min-width:16px;}.sizeInfoTip{font-size:11px;}.timeInfoTip{width:150px;height:40px;font-size:11px;}.timeInfoTipBar,.timeInfoTipEventBar{position:relative;display:block;margin:0;opacity:1;height:15px;width:4px;}.timeInfoTipStartLabel{color:gray;}.timeInfoTipSeparator{padding-top:10px;color:gray;}.timeInfoTipSeparator SPAN{white-space:pre-wrap;}.timeInfoTipEventBar{width:1px !important;}.netWindowLoadBar.timeInfoTipBar,.netContentLoadBar.timeInfoTipBar{width:1px;}.netSummaryRow .netTimeLabel,.loaded .netTimeLabel{background:transparent;}.loaded .netTimeBar{background:#B6B6B6 url(images/netBarLoaded.gif) repeat-x;border-color:#B6B6B6;}.fromCache .netTimeBar{background:#D6D6D6 url(images/netBarCached.gif) repeat-x;border-color:#D6D6D6;}.netSummaryRow .netTimeBar{background:#BBBBBB;border:none;display:inline-block;}.timeInfoTipCell.startTime{padding-right:25px;}.timeInfoTipCell.elapsedTime{text-align:right;padding-right:8px;}.netSummaryLabel{color:#222222;}.netSummaryRow{background:#BBBBBB !important;font-weight:bold;}.netSummaryRow TD{padding:1px 0 2px 0 !important;}.netSummaryRow > .netCol{border-top:1px solid #999999;border-bottom:1px solid;border-bottom-color:#999999;padding-top:1px;}.netSummaryRow > .netCol:first-child{border-left:1px solid #999999;}.netSummaryRow > .netCol:last-child{border-right:1px solid #999999;}.netCountLabel{padding-left:18px;}.netTotalSizeCol{text-align:right;}.netTotalTimeCol{text-align:right;}.netCacheSizeLabel{display:inline-block;float:left;padding-left:6px;}.netTotalTimeLabel{padding-right:6px;}.netInfoCol{border-top:1px solid #EEEEEE;background:url(images/timeline-sprites.png) repeat-x scroll 0 -112px #FFFFFF;padding-left:10px;padding-bottom:4px;}.isExpandable .netHrefLabel{background-image:url(images/twisty-sprites.png);background-repeat:no-repeat;background-position:3px 3px;}.netRow.opened > .netCol > .netHrefLabel{background-image:url(images/twisty-sprites.png);background-position:3px -16px;}.netSizerRow,.netSizerRow > .netCol{border:0;padding:0;}.netCol{display:none;}.netCol.netOptionsCol{display:table-cell;}#content[previewCols~=url] TD.netHrefCol,#content[previewCols~=status] TD.netStatusCol,#content[previewCols~=domain] TD.netDomainCol,#content[previewCols~=size] TD.netSizeCol,#content[previewCols~=timeline] TD.netTimeCol,#content[previewCols~=type] TD.netTypeCol{display:table-cell;}/* See license.txt for terms of usage */ .requestBodyBodies{border-left:1px solid #D7D7D7;border-right:1px solid #D7D7D7;border-bottom:1px solid #D7D7D7;}.netInfoRow .tabView{width:99%;}.netInfoText{padding:8px;background-color:#FFFFFF;font-family:Monaco,monospace;}.netInfoText[selected="true"]{display:block;}.netInfoParamName{padding:0 10px 0 0;font-family:Lucida Grande,Tahoma,sans-serif;font-weight:bold;vertical-align:top;text-align:right;white-space:nowrap;}.netInfoParamValue > PRE{margin:0}.netInfoHeadersText,.netInfoCookiesText{padding-top:0;width:100%;}.netInfoParamValue{width:100%;}.netInfoHeadersGroup,.netInfoCookiesGroup{margin-bottom:4px;border-bottom:1px solid #D7D7D7;padding-top:8px;padding-bottom:2px;font-family:Lucida Grande,Tahoma,sans-serif;font-weight:bold;color:#565656;}.netInfoHtmlPreview{border:0;width:100%;height:100px;}.netInfoHtmlText{padding:0;}.htmlPreviewResizer{width:100%;height:4px;background-image:url(images/splitterh.png);background-repeat:repeat-x;cursor:s-resize;}body[hResizing="true"] .netInfoHtmlPreview{pointer-events:none !important;}/* See license.txt for terms of usage */ .pageStatsBody[class~=opened]{border-bottom:1px solid #EEEEEE;}.pagePieTable{margin:7px;border-right:1px solid #EEEEEE;padding-right:7px;display:inline-table;}.pieGraph{width:100px;height:100px;display:block;}.pieLabel{font-size:11px;padding-left:10px;cursor:default;}.pieLabel SPAN{vertical-align:middle;}.pieLabel .box{display:inline-block;width:10px;height:10px;margin-top:1px;}.pieLabel .label{padding-left:5px;}/* See license.txt for terms of usage */ .pageTimeline{background-color:#FFFFFF;color:#000000;}.pageTimelineBody{width:100%;}.pageTimelineBody[class~=opened]{border-bottom:1px solid #EEEEEE;}.pageTimelineTable{height:100px;padding-left:5px;}.pageTimelineCol{vertical-align:bottom;padding-left:4px;outline:none;-moz-outline-style:none;-moz-user-focus:ignore;}.pageBar{width:9px;background:url(images/page-timeline.png) repeat-y scroll 0px 0px #FFFFFF;cursor:pointer;}.pageBar.selected{opacity:0.5;-moz-opacity:0.50:filter:alpha(opacity=50);}.pageTimelineCol:hover .pageBar{background:url(images/page-timeline.png) repeat-y scroll -8px 0px #FFFFFF;}.pageTimelineBody .connector{margin-left:16px;display:block;background:url(images/tooltipConnectorUp.png) no-repeat;width:16px;height:11px;position:relative;margin-bottom:-1px;}.pageDescBox .desc{font-size:11px;border:1px solid rgb(126,171,205);background:url(images/tabEnabled.png) repeat-x scroll 0px 0px rgb(234,234,234);padding:3px;-moz-border-radius:3px;-webkit-border-radius:3px;border-radius:3px;}.pageDescBox .desc .summary{font-weight:bold;}.pageDescBox .desc .time{padding-left:10px;}.pageDescBox .desc .title{color:black;padding-left:10px;}.pageDescBox .desc .comment{color:gray;padding-top:1px;}/* See license.txt for terms of usage */ .errorTable{margin:8px;font-size:13px;}.errorProperty{color:gray;font-style:italic;}.errorMessage{color:red;}.errorRow:hover{background:#EFEFEF;cursor:pointer;}.errorOptionsTarget{width:11px;height:10px;background:url(images/contextMenuTarget.png) no-repeat;visibility:collapse;}.errorOptionsTarget:hover{background-image:url(images/contextMenuTargetHover.png);}.errorRow:hover > .errorOptions.hasTarget > .errorOptionsTarget{visibility:visible;}/* See license.txt for terms of usage */ .AboutTab .version{font-size:11px;color:#DD467B;}.tabAboutBody{font-family:Verdana,Geneva,Arial,Helvetica,sans-serif;font-size:11.7px;font-style:normal;font-weight:400;}.aboutBody{padding:8px;-webkit-user-select:text;}.tabAboutBody code{color:green;}/* See license.txt for terms of usage */ .tabHomeBody{font-family:Verdana,Geneva,Arial,Helvetica,sans-serif;font-size:13px;font-style:normal;font-weight:400;}.homeBody{padding:8px;}.overlay{height:0;width:100%;position:fixed;z-index:10;left:0;top:30px;background-color:#eaebeb;overflow-x:hidden;opacity:0.8;}.overlay-content{font-size:1.2em;position:relative;top:40%;width:100%;text-align:center;}/* See license.txt for terms of usage */ .tabDOMBody{font-family:Lucida Grande,Tahoma,sans-serif;font-size:11px;}.tabDOMBody .domContent{position:absolute;top:28px;bottom:0px;overflow:auto;width:100%;-webkit-user-select:text;}.tabDOMBody .domContent .domBox{width:100%;}.tabDOMBody .domContent .domBox .title{color:gray;padding:8px 0 0 8px;}.tabDOMBody .separator{border-bottom:1px solid #D7D7D7;}.tabDOMBody .domTable{padding:5px;}.domToolbar > .toolbar{text-align:right;}.tabDOMBody .domContent .domBox .content,.tabDOMBody .domContent .domBox .results{vertical-align:top;}.resultsDefaultContent{color:#D7D7D7;font-size:12px;font-family:Lucida Grande,Tahoma,sans-serif;margin:60px;text-align:center;}.queryResultsViewType{border-bottom:1px solid #EEEEEE;display:block;padding:5px;}.queryResultsViewType .type{width:13px;height:13px;padding:0;margin:0;vertical-align:bottom;}.queryResultsViewType .label{padding-left:5px;}.domBox .splitter{width:4px;cursor:e-resize;background-color:#D7D7D7;}.domBox .splitter,.domBox .results{visibility:collapse;}.domBox .splitter.visible,.domBox .results.visible{visibility:visible;}/* See license.txt for terms of usage */ .tabSchemaBody{font-family:Monaco,monospace;font-size:12px;}/* See license.txt for terms of usage */ .previewList{-webkit-user-select:text;}.harDownloadButton OBJECT{outline:none;-moz-user-focus:ignore;}.harSaveButton{background:url(images/save.png) no-repeat;}/* See license.txt for terms of usage */ .searchTextBox{vertical-align:sub;margin-right:3px;}.searchInput{outline:none;border:1px solid #EEEEEE;padding:1px 17px 1px 3px;width:300px;}.searchInput[status=notfound]{background:rgb(255,102,102);color:white;}.searchBox .arrow{width:11px;height:10px;background:url(images/contextMenuTarget.png) no-repeat;display:inline-block;position:relative;margin-left:-15px;top:1px;cursor:pointer;}.searchBox .arrow:hover{background-image:url(images/contextMenuTargetHover.png);}.searchBox .arrow[disabled=true]{display:none;}.searchBox .resizer{cursor:e-resize;}/* See license.txt for terms of usage */ body[vResizing="true"] *{cursor:e-resize !important;}body[hResizing="true"] *{cursor:s-resize !important;}