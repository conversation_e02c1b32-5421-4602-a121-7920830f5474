/* See license.txt for terms of usage */

define(
{
    "root": {
        "fromCache": "From Cache",
        "menuBreakLayout": "Break Timeline Layout",
        "menuOpenRequestInWindow": "Open Request in New Window",
        "menuOpenResponseInWindow": "Open Response in New Window",
        "request": "Request",
        "requests": "Requests",

        "tooltipSize": "%S (%S bytes)",
        "tooltipZippedSize": "%S (%S bytes) - compressed",
        "tooltipUnzippedSize": "%S (%S bytes) - uncompressed",
        "unknownSize": "Unknown size",

        "request.Started": "Request start time since the beginning",
        "request.phases.label": "Request phases start and elapsed time relative to the request start:",
        "request.phase.Resolving": "DNS Lookup",
        "request.phase.Connecting": "Connecting",
        "request.phase.Blocking": "Blocking",
        "request.phase.Sending": "Sending",
        "request.phase.Waiting": "Waiting",
        "request.phase.Receiving": "Receiving",

        "request.timings.label": "Event timing relative to the request start:",
        "ContentLoad": "DOM Loaded",
        "WindowLoad": "Page Loaded",
        "page.event.Load": "Page Loaded",

        "menuBreakTimeline": "Break Timeline Layout",
        "menuOpenRequest": "Open Request in New Window",
        "menuOpenResponse": "Open Response in New Window"
    }
});
