/* See license.txt for terms of usage */

define(
{
    "root": {
        "pieLabelDNS": "DNS",
        "pieLabelSSL": "SSL/TLS",
        "pieLabelConnect": "Connect",
        "pieLabelBlocked": "Blocked",
        "pieLabelSend": "Send",
        "pieLabelWait": "Wait",
        "pieLabelReceive": "Receive",

        "pieLabelHTMLText": "HTML/Text",
        "pieLabelJavaScript": "JavaScript",
        "pieLabelCSS": "CSS",
        "pieLabelImage": "Image",
        "pieLabelFlash": "Flash",
        "pieLabelOthers": "Others",

        "pieLabelHeadersSent": "Headers Sent",
        "pieLabelBodiesSent": "Bodies Sent",
        "pieLabelHeadersReceived": "Headers Received",
        "pieLabelBodiesReceived": "Bodies Received",

        "pieLabelDownloaded": "Downloaded",
        "pieLabelPartial": "Partial",
        "pieLabelFromCache": "From Cache"
    }
});
