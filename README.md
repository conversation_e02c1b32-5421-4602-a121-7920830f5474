# HTTP Archive Viewer - Chrome Extension

This is an HTTP Archive (HAR) file viewer converted from a deprecated Chrome App to a modern Chrome Extension using Manifest V3.

## What Changed

The extension has been updated from:
- **Chrome App** (deprecated and no longer supported)
- **Manifest V2** (being phased out)

To:
- **Chrome Extension** (modern and supported)
- **Manifest V3** (current standard)

## Installation

1. Open Chrome and go to `chrome://extensions/`
2. Enable "Developer mode" in the top right corner
3. Click "Load unpacked" and select this folder
4. The HAR Viewer extension should now appear in your extensions list

## Usage

1. Click the HAR Viewer icon in your Chrome toolbar
2. A launcher page will open with information about the HAR viewer
3. Click "Launch HAR Viewer" to open the actual viewer in a new tab
4. You can now drag and drop HAR files or use the interface to load and analyze HTTP archives

## Features

- View HTTP Archive (HAR) files
- Analyze network requests and responses
- Timeline visualization
- Request/response details
- Performance analysis

## Technical Details

- **Manifest Version**: 3
- **Service Worker**: Replaces the old Chrome App background script
- **Extension Action**: Provides a popup interface to launch the viewer
- **Content Security Policy**: Configured to allow RequireJ<PERSON> and jQuery to function properly

## Files Structure

- `manifest.json` - Extension configuration (Manifest V3)
- `background.js` - Service worker for handling extension actions
- `launcher.html` - Launcher page with instructions and link to the viewer
- `harviewer/` - The main HAR viewer application
- `icon-128.png` - Extension icon

The HAR viewer itself remains unchanged and fully functional.
