<div>
<h2>HTTP Archive Viewer</h2>
<table style="width:600px;">
<tr><td>
<p>The purpose of this online tool is to visualize
<a href="http://www.softwareishard.com/blog/har-12-spec/">
    HTTP Archive (HAR)</a>
log files created by HTTP tracking tools. These files contain log of HTTP
client/server conversation and can be used for an additional analysis of e.g. 
page load performance.</p>

<p>User interface of this tool is composed from the following tabs:</p>
<ul>
<li><b>Load</b> - Paste content of a log file into the text box in this tab.</li>
<li><b>Inspect</b> - Switch to this tab if you want to see visualised HTTP traffic.</li>
</ul>
</td></tr>

<tr><td>
<h3>HTTP Archive Specification</h3>
<p>Required
<a href="http://www.softwareishard.com/blog/har-12-spec/">
structure</a> of the input HTTP Archive file (*.har) is described using
<a href="http://www.json.com/json-schema-proposal/">JSON Schema</a>.
You can explore the current schema definition within the <span class="linkSchema link">Schema</span>
tab on this page.</p>
</td></tr>

<tr><td>
<h3>Request Timing Fields</h3>
<p>Part of the HTTP log is also a timing info about network request executions.
Here is a description of individual request/response phases:</p>
<ul>
<li><i>Blocking</i> - Time spent in a queue waiting for a network connection.</li>
<li><i>DNS Lookup</i> - DNS resolution time. The time required to resolve a host name.</li>
<li><i>Connecting</i> - Time required to create TCP connection.</li>
<li><i>Sending</i> - Time required to send HTTP request to the server.</li>
<li><i>Waiting</i> - Waiting for a response from the server.</li>
<li><i>Receiving</i> - Time required to read entire response from the server (or cache).</li>
</ul>
</td></tr>

<tr><td>
<h3>Online Log Files</h3>
<p>HAR Viewer also supports JSONP and so, it's possible to load log files 
from different domains. This allows linking your online logs and preview them
automatically within the viewer. See live
<a href="?inputUrl=http://www.janodvarko.cz/har/viewer/examples/inline-scripts-block.harp">example</a>.
</p>

<p><i>1. The Content of a *.har file must be enclosed within a callback function:</i></p>
<code>onInputData({ "log": { ... } })</code>

<p><i>2. The link displaying a *.har file (using this viewer) must specify URL of
the file in <b>inputUrl</b> parameter:</i></p>
<code>http://www.softwareishard.com/har/viewer/?inputUrl=http://www.example.com/netData.har</code>

<p><i>3. A custom name of the callback function can be specified in a <b>callback</b> parameter
(by default it's <b>onInputData</b>):</i></p>
<code>http://www.softwareishard.com/har/viewer/?inputUrl=http://www.example.com/netData.har&amp;callback=onInputData</code>
<br/><br/>
</td></tr>

</table>

<br/><br/>
<i>HAR Viewer author: Jan Odvarko, <EMAIL></i>
</div>
